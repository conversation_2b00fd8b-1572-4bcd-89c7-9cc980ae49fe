<template>
  <form @submit.prevent="handleSubmit">
    <div class="field">
      <label class="label">User ID (Optional)</label>
      <div class="control">
        <input
          type="text"
          class="input"
          v-model="form.userId"
          placeholder="Enter user ID or leave empty"
        />
      </div>
    </div>

    <div class="field">
      <label class="label">Region *</label>
      <div class="control">
        <input
          type="text"
          class="input"
          v-model="form.region"
          placeholder="Enter region"
          required
        />
      </div>
    </div>

    <div class="field">
      <label class="label">City *</label>
      <div class="control">
        <input
          type="text"
          class="input"
          v-model="form.city"
          placeholder="Enter city"
          required
        />
      </div>
    </div>

    <div class="field">
      <label class="label">Street *</label>
      <div class="control">
        <input
          type="text"
          class="input"
          v-model="form.street"
          placeholder="Enter street address"
          required
        />
      </div>
    </div>

    <div class="field">
      <label class="label">Postal Code *</label>
      <div class="control">
        <input
          type="text"
          class="input"
          v-model="form.postalCode"
          placeholder="Enter postal code"
          required
        />
      </div>
    </div>

    <div class="field is-grouped">
      <div class="control">
        <button 
          type="submit" 
          class="button is-primary"
          :class="{ 'is-loading': loading }"
          :disabled="loading"
        >
          {{ address ? 'Update' : 'Create' }} Address
        </button>
      </div>
      <div class="control">
        <button 
          type="button" 
          class="button is-light"
          @click="$emit('cancel')"
          :disabled="loading"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';

// Props
const props = defineProps({
  address: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['submit', 'cancel']);

// Form data
const form = ref({
  userId: '',
  region: '',
  city: '',
  street: '',
  postalCode: ''
});

// Watch for address changes (for editing)
watch(() => props.address, (newAddress) => {
  if (newAddress) {
    form.value = {
      userId: newAddress.userId || '',
      region: newAddress.addressVO?.region || newAddress.region || '',
      city: newAddress.addressVO?.city || newAddress.city || '',
      street: newAddress.addressVO?.street || newAddress.street || '',
      postalCode: newAddress.addressVO?.postalCode || newAddress.postalCode || ''
    };
  } else {
    // Reset form for new address
    form.value = {
      userId: '',
      region: '',
      city: '',
      street: '',
      postalCode: ''
    };
  }
}, { immediate: true });

// Methods
const handleSubmit = () => {
  const submitData = {
    ...form.value,
    addressVO: {
      region: form.value.region,
      city: form.value.city,
      street: form.value.street,
      postalCode: form.value.postalCode
    }
  };
  
  // Remove empty userId
  if (!submitData.userId) {
    delete submitData.userId;
  }
  
  emit('submit', submitData);
};
</script>

<style scoped>
.field {
  margin-bottom: 1rem;
}

.label {
  color: var(--text-primary);
  font-weight: 500;
}

.input {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.125em rgba(59, 130, 246, 0.25);
}

.button.is-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.button.is-primary:hover {
  background-color: var(--accent-color-dark);
}

.button.is-light {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.button.is-light:hover {
  background-color: var(--card-bg);
}
</style>

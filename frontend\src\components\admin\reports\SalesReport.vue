<template>
  <div class="sales-report">
    <!-- Sales Metrics Grid -->
    <div class="metrics-section">
      <h3 class="section-title">
        <i class="fas fa-chart-line"></i>
        Sales Performance
      </h3>
      
      <div class="metrics-grid">
        <div 
          v-for="metric in salesMetrics" 
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-change" v-if="metric.changePercentage">
              <i :class="getChangeIcon(metric.changePercentage)"></i>
              <span>{{ Math.abs(metric.changePercentage).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-comparison" v-if="metric.previousValue">
              vs {{ formatMetricValue(metric.previousValue, metric.type) }} last period
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Trend Chart -->
    <div class="chart-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">Sales Trend</h3>
          <div class="chart-controls">
            <select v-model="chartPeriod" @change="updateChart" class="chart-select">
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
        
        <div class="chart-content">
          <canvas ref="salesChartCanvas" id="sales-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- Top Products -->
    <div class="top-products-section">
      <h3 class="section-title">
        <i class="fas fa-trophy"></i>
        Top Selling Products
      </h3>
      
      <div class="products-grid">
        <div 
          v-for="(product, index) in topProducts" 
          :key="product.id"
          class="product-card"
        >
          <div class="product-rank">
            <span class="rank-number">#{{ index + 1 }}</span>
            <div class="rank-badge" :class="`rank-${index + 1}`">
              <i v-if="index === 0" class="fas fa-crown"></i>
              <i v-else-if="index === 1" class="fas fa-medal"></i>
              <i v-else-if="index === 2" class="fas fa-award"></i>
              <i v-else class="fas fa-star"></i>
            </div>
          </div>
          
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-category">{{ product.category }}</div>
          </div>
          
          <div class="product-stats">
            <div class="stat-item">
              <span class="stat-label">Sales</span>
              <span class="stat-value">{{ formatNumber(product.sales) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Revenue</span>
              <span class="stat-value">{{ formatCurrency(product.revenue) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales by Category -->
    <div class="category-section">
      <div class="category-container">
        <div class="category-header">
          <h3 class="category-title">Sales by Category</h3>
        </div>
        
        <div class="category-content">
          <div class="chart-wrapper">
            <canvas ref="categoryChartCanvas" id="category-chart"></canvas>
          </div>
          
          <div class="category-legend">
            <div 
              v-for="(item, index) in categoryData" 
              :key="item.category"
              class="legend-item"
            >
              <div 
                class="legend-color" 
                :style="{ backgroundColor: getCategoryColor(index) }"
              ></div>
              <div class="legend-info">
                <div class="legend-label">{{ item.category }}</div>
                <div class="legend-value">
                  {{ formatNumber(item.sales) }} sales ({{ item.percentage.toFixed(1) }}%)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Insights -->
    <div class="insights-section">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        Sales Insights
      </h3>
      
      <div class="insights-grid">
        <div 
          v-for="insight in salesInsights" 
          :key="insight.id"
          class="insight-card"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
          </div>
          <div class="insight-priority">
            <div class="priority-indicator" :class="`priority-${insight.priority}`">
              {{ insight.priority }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Performance Table -->
    <div class="performance-section">
      <h3 class="section-title">
        <i class="fas fa-table"></i>
        Sales Performance Details
      </h3>
      
      <div class="performance-table">
        <div class="table-controls">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input 
              v-model="performanceSearch" 
              type="text" 
              placeholder="Search products..."
              class="search-input"
            />
          </div>
          
          <div class="filter-controls">
            <select v-model="categoryFilter" class="filter-select">
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>
        </div>
        
        <div class="table-container">
          <table class="performance-table-element">
            <thead>
              <tr>
                <th @click="sortBy('name')" class="sortable">
                  Product Name
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('category')" class="sortable">
                  Category
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('sales')" class="sortable">
                  Sales
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('revenue')" class="sortable">
                  Revenue
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('avgPrice')" class="sortable">
                  Avg Price
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('growth')" class="sortable">
                  Growth
                  <i class="fas fa-sort sort-icon"></i>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in paginatedPerformance" :key="item.id" class="performance-row">
                <td>
                  <div class="product-cell">
                    <div class="product-name">{{ item.name }}</div>
                    <div class="product-sku">SKU: {{ item.sku }}</div>
                  </div>
                </td>
                <td>
                  <span class="category-badge">{{ item.category }}</span>
                </td>
                <td>
                  <span class="sales-number">{{ formatNumber(item.sales) }}</span>
                </td>
                <td>
                  <span class="revenue-amount">{{ formatCurrency(item.revenue) }}</span>
                </td>
                <td>
                  <span class="price-amount">{{ formatCurrency(item.avgPrice) }}</span>
                </td>
                <td>
                  <span class="growth-indicator" :class="getGrowthClass(item.growth)">
                    <i :class="getGrowthIcon(item.growth)"></i>
                    {{ Math.abs(item.growth).toFixed(1) }}%
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="pagination" v-if="totalPages > 1">
          <button 
            @click="currentPage = 1" 
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button 
            @click="currentPage--" 
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="fas fa-angle-left"></i>
          </button>
          
          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          
          <button 
            @click="currentPage++" 
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="fas fa-angle-right"></i>
          </button>
          <button 
            @click="currentPage = totalPages" 
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'SalesReport',
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // Refs
    const salesChartCanvas = ref(null)
    const categoryChartCanvas = ref(null)
    const chartPeriod = ref('daily')
    const performanceSearch = ref('')
    const categoryFilter = ref('')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const sortField = ref('sales')
    const sortDirection = ref('desc')

    // Chart instances
    let salesChart = null
    let categoryChart = null

    // Computed properties
    const salesMetrics = computed(() => {
      return props.data?.metrics?.items || []
    })

    const salesInsights = computed(() => {
      return props.data?.insights || []
    })

    const topProducts = computed(() => {
      const tableData = props.data?.table?.data || []
      return tableData
        .sort((a, b) => (b.sales || 0) - (a.sales || 0))
        .slice(0, 5)
        .map(product => ({
          id: product.id,
          name: product.name,
          category: product.category,
          sales: product.sales || 0,
          revenue: product.revenue || 0
        }))
    })

    const categoryData = computed(() => {
      const secondaryChart = props.data?.charts?.secondary
      if (!secondaryChart?.data?.labels) return []

      const labels = secondaryChart.data.labels
      const data = secondaryChart.data.datasets[0]?.data || []
      const total = data.reduce((sum, value) => sum + value, 0)

      return labels.map((category, index) => ({
        category,
        sales: data[index] || 0,
        percentage: total > 0 ? (data[index] / total) * 100 : 0
      }))
    })

    const categories = computed(() => {
      const tableData = props.data?.table?.data || []
      const uniqueCategories = [...new Set(tableData.map(item => item.category).filter(Boolean))]
      return uniqueCategories.sort()
    })

    const performanceData = computed(() => {
      return props.data?.table?.data || []
    })

    const filteredPerformance = computed(() => {
      let filtered = performanceData.value

      // Apply search filter
      if (performanceSearch.value) {
        const query = performanceSearch.value.toLowerCase()
        filtered = filtered.filter(item =>
          item.name?.toLowerCase().includes(query) ||
          item.sku?.toLowerCase().includes(query) ||
          item.category?.toLowerCase().includes(query)
        )
      }

      // Apply category filter
      if (categoryFilter.value) {
        filtered = filtered.filter(item => item.category === categoryFilter.value)
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aVal = a[sortField.value]
        const bVal = b[sortField.value]

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
        }

        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortDirection.value === 'asc') {
          return aStr.localeCompare(bStr)
        } else {
          return bStr.localeCompare(aStr)
        }
      })

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredPerformance.value.length / itemsPerPage.value)
    })

    const paginatedPerformance = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredPerformance.value.slice(start, end)
    })

    // Methods
    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatNumber = (value) => {
      return reportsService.formatNumber(value)
    }

    const getChangeIcon = (changePercentage) => {
      if (changePercentage > 0) return 'fas fa-arrow-up'
      if (changePercentage < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const getCategoryColor = (index) => {
      const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b', '#38f9d7']
      return colors[index % colors.length]
    }

    const getGrowthClass = (growth) => {
      if (growth > 0) return 'positive'
      if (growth < 0) return 'negative'
      return 'neutral'
    }

    const getGrowthIcon = (growth) => {
      if (growth > 0) return 'fas fa-arrow-up'
      if (growth < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const sortBy = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const updateChart = () => {
      createSalesChart()
    }

    const createSalesChart = async () => {
      await nextTick()

      if (salesChart) {
        salesChart.destroy()
      }

      if (!salesChartCanvas.value || !props.data?.charts?.primary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = salesChartCanvas.value.getContext('2d')

        salesChart = new Chart(ctx, {
          type: 'line',
          data: props.data.charts.primary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatNumber(context.parsed.y)
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Sales Count'
                },
                ticks: {
                  callback: function(value) {
                    return formatNumber(value)
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              axis: 'x',
              intersect: false
            }
          }
        })
      } catch (error) {
        console.error('Error creating sales chart:', error)
      }
    }

    const createCategoryChart = async () => {
      await nextTick()

      if (categoryChart) {
        categoryChart.destroy()
      }

      if (!categoryChartCanvas.value || !props.data?.charts?.secondary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = categoryChartCanvas.value.getContext('2d')

        categoryChart = new Chart(ctx, {
          type: 'doughnut',
          data: props.data.charts.secondary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false // We'll use custom legend
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((sum, value) => sum + value, 0)
                    const percentage = ((context.parsed / total) * 100).toFixed(1)
                    return context.label + ': ' + formatNumber(context.parsed) + ' sales (' + percentage + '%)'
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('Error creating category chart:', error)
      }
    }

    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createSalesChart()
        createCategoryChart()
      })
    }, { deep: true })

    watch([performanceSearch, categoryFilter], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createSalesChart()
        createCategoryChart()
      })
    })

    onUnmounted(() => {
      if (salesChart) {
        salesChart.destroy()
      }
      if (categoryChart) {
        categoryChart.destroy()
      }
    })

    return {
      salesChartCanvas,
      categoryChartCanvas,
      chartPeriod,
      performanceSearch,
      categoryFilter,
      currentPage,
      itemsPerPage,
      salesMetrics,
      salesInsights,
      topProducts,
      categoryData,
      categories,
      filteredPerformance,
      totalPages,
      paginatedPerformance,
      formatMetricValue,
      formatCurrency,
      formatNumber,
      getChangeIcon,
      getCategoryColor,
      getGrowthClass,
      getGrowthIcon,
      sortBy,
      updateChart
    }
  }
}
</script>

<style scoped>
.sales-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Metrics Section */
.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #f8fafc 100%);
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #f8fafc 100%);
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.positive .metric-change {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.negative .metric-change {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.neutral .metric-change {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-comparison {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Chart Section */
.chart-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.chart-content {
  height: 400px;
  position: relative;
}

/* Top Products Section */
.top-products-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.products-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.product-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.product-rank {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 80px;
}

.rank-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
}

.rank-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); }
.rank-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e5e7eb 100%); }
.rank-3 { background: linear-gradient(135deg, #cd7f32 0%, #d97706 100%); }
.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.product-category {
  font-size: 0.875rem;
  color: #6b7280;
}

.product-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .product-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .product-stats {
    width: 100%;
    justify-content: space-around;
  }

  .chart-content {
    height: 300px;
  }

  .metric-value {
    font-size: 1.5rem;
  }
}

/* Category Section */
.category-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.category-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
  align-items: center;
}

.chart-wrapper {
  height: 300px;
  position: relative;
}

.category-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Insights Section */
.insights-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.insights-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
  background: #f9fafb;
  transition: all 0.2s;
}

.insight-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.insight-card.positive {
  background: linear-gradient(135deg, #f0fdf4 0%, #f9fafb 100%);
  border-left-color: #10b981;
}

.insight-card.negative {
  background: linear-gradient(135deg, #fef2f2 0%, #f9fafb 100%);
  border-left-color: #ef4444;
}

.insight-card.warning {
  background: linear-gradient(135deg, #fffbeb 0%, #f9fafb 100%);
  border-left-color: #f59e0b;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.positive .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.negative .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.insight-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.insight-priority {
  display: flex;
  align-items: center;
}

.priority-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.priority-1 { background: #9ca3af; }
.priority-2 { background: #6b7280; }
.priority-3 { background: #f59e0b; }
.priority-4 { background: #ef4444; }
.priority-5 { background: #dc2626; }

/* Performance Section */
.performance-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 300px;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.performance-table-element {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.performance-table-element th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  white-space: nowrap;
}

.performance-table-element th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.performance-table-element th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  opacity: 0.5;
  font-size: 0.75rem;
}

.performance-table-element td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.performance-row:hover {
  background: #f9fafb;
}

.product-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 500;
  color: #111827;
}

.product-sku {
  font-size: 0.75rem;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.category-badge {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
}

.sales-number {
  font-weight: 600;
  color: #111827;
}

.revenue-amount {
  font-weight: 600;
  color: #10b981;
}

.price-amount {
  font-weight: 500;
  color: #374151;
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 4px;
}

.growth-indicator.positive {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.growth-indicator.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.growth-indicator.neutral {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 16px;
}

@media (max-width: 768px) {
  .category-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
  }

  .performance-table-element {
    font-size: 0.875rem;
  }

  .performance-table-element th,
  .performance-table-element td {
    padding: 8px 12px;
  }
}
</style>

// Product Constants
// These constants should match the backend enums exactly

// Currency Enum (matching backend Currency enum)
export const CURRENCY = {
  UAH: 0,         // Currency.UAH = 0
  USD: 1,         // Currency.USD = 1
  EUR: 2          // Currency.EUR = 2
};

// Product Status Enum (matching backend ProductStatus enum)
export const PRODUCT_STATUS = {
  PENDING: 0,     // ProductStatus.Pending = 0
  APPROVED: 1,    // ProductStatus.Approved = 1
  REJECTED: 2,    // ProductStatus.Rejected = 2
  ARCHIVED: 3     // ProductStatus.Archived = 3
};

// Currency mapping for API calls
export const CURRENCY_MAP = {
  'UAH': CURRENCY.UAH,
  'USD': CURRENCY.USD,
  'EUR': CURRENCY.EUR
};

// Reverse mapping for display
export const CURRENCY_TEXT = {
  [CURRENCY.UAH]: 'UAH',
  [CURRENCY.USD]: 'USD',
  [CURRENCY.EUR]: 'EUR'
};

// Product Status mapping for API calls
export const PRODUCT_STATUS_MAP = {
  'PENDING': PRODUCT_STATUS.PENDING,
  'APPROVED': PRODUCT_STATUS.APPROVED,
  'REJECTED': PRODUCT_STATUS.REJECTED,
  'ARCHIVED': PRODUCT_STATUS.ARCHIVED
};

// Reverse mapping for display
export const PRODUCT_STATUS_TEXT = {
  [PRODUCT_STATUS.PENDING]: 'Pending',
  [PRODUCT_STATUS.APPROVED]: 'Approved',
  [PRODUCT_STATUS.REJECTED]: 'Rejected',
  [PRODUCT_STATUS.ARCHIVED]: 'Archived'
};

// Currency options for UI components
export const CURRENCY_OPTIONS = [
  { value: 'UAH', label: 'UAH', enumValue: CURRENCY.UAH },
  { value: 'USD', label: 'USD', enumValue: CURRENCY.USD },
  { value: 'EUR', label: 'EUR', enumValue: CURRENCY.EUR }
];

// Product Status options for UI components
export const PRODUCT_STATUS_OPTIONS = [
  { value: PRODUCT_STATUS.PENDING, label: 'Pending' },
  { value: PRODUCT_STATUS.APPROVED, label: 'Approved' },
  { value: PRODUCT_STATUS.REJECTED, label: 'Rejected' },
  { value: PRODUCT_STATUS.ARCHIVED, label: 'Archived' }
];

// Product Status CSS classes
export const PRODUCT_STATUS_CLASSES = {
  [PRODUCT_STATUS.PENDING]: 'is-warning',
  [PRODUCT_STATUS.APPROVED]: 'is-success',
  [PRODUCT_STATUS.REJECTED]: 'is-danger',
  [PRODUCT_STATUS.ARCHIVED]: 'is-info'
};

// Helper functions
export const getCurrencyEnumValue = (currencyString) => {
  return CURRENCY_MAP[currencyString] ?? CURRENCY.UAH;
};

export const getCurrencyText = (currencyEnum) => {
  return CURRENCY_TEXT[currencyEnum] ?? 'UAH';
};

export const getProductStatusEnumValue = (statusString) => {
  return PRODUCT_STATUS_MAP[statusString] ?? PRODUCT_STATUS.PENDING;
};

export const getProductStatusText = (statusEnum) => {
  return PRODUCT_STATUS_TEXT[statusEnum] ?? 'Pending';
};

// Validation helpers
export const isValidCurrency = (currency) => {
  return Object.values(CURRENCY).includes(currency) || Object.keys(CURRENCY_MAP).includes(currency);
};

export const isValidProductStatus = (status) => {
  return Object.values(PRODUCT_STATUS).includes(status) || Object.keys(PRODUCT_STATUS_MAP).includes(status);
};

// API transformation helpers
export const transformProductDataForAPI = (productData) => {
  return {
    ...productData,
    priceCurrency: getCurrencyEnumValue(productData.priceCurrency),
    status: typeof productData.status === 'string' 
      ? getProductStatusEnumValue(productData.status) 
      : (productData.status ?? PRODUCT_STATUS.PENDING),
    priceAmount: productData.priceAmount ? parseFloat(productData.priceAmount) : null,
    stock: productData.stock ? parseInt(productData.stock) : 0,
    attributes: productData.attributes 
      ? (typeof productData.attributes === 'string' 
          ? JSON.parse(productData.attributes) 
          : productData.attributes) 
      : {}
  };
};

export const transformProductDataFromAPI = (apiData) => {
  return {
    ...apiData,
    priceCurrency: getCurrencyText(apiData.priceCurrency),
    status: getProductStatusText(apiData.status),
    attributes: typeof apiData.attributes === 'string' 
      ? JSON.parse(apiData.attributes) 
      : (apiData.attributes || {})
  };
};

<template>
  <nav class="pagination is-centered" role="navigation" aria-label="pagination">
    <!-- Previous Button -->
    <button
      class="pagination-previous"
      :disabled="currentPage <= 1"
      @click="goToPage(currentPage - 1)"
    >
      <span class="icon">
        <i class="fas fa-chevron-left"></i>
      </span>
      <span>Previous</span>
    </button>

    <!-- Next Button -->
    <button
      class="pagination-next"
      :disabled="currentPage >= totalPages"
      @click="goToPage(currentPage + 1)"
    >
      <span>Next</span>
      <span class="icon">
        <i class="fas fa-chevron-right"></i>
      </span>
    </button>

    <!-- Page Numbers -->
    <ul class="pagination-list">
      <!-- First Page -->
      <li v-if="showFirstPage">
        <button
          class="pagination-link"
          :class="{ 'is-current': currentPage === 1 }"
          @click="goToPage(1)"
        >
          1
        </button>
      </li>

      <!-- First Ellipsis -->
      <li v-if="showFirstEllipsis">
        <span class="pagination-ellipsis">&hellip;</span>
      </li>

      <!-- Visible Pages -->
      <li v-for="page in visiblePages" :key="page">
        <button
          class="pagination-link"
          :class="{ 'is-current': currentPage === page }"
          @click="goToPage(page)"
        >
          {{ page }}
        </button>
      </li>

      <!-- Last Ellipsis -->
      <li v-if="showLastEllipsis">
        <span class="pagination-ellipsis">&hellip;</span>
      </li>

      <!-- Last Page -->
      <li v-if="showLastPage">
        <button
          class="pagination-link"
          :class="{ 'is-current': currentPage === totalPages }"
          @click="goToPage(totalPages)"
        >
          {{ totalPages }}
        </button>
      </li>
    </ul>

    <!-- Page Info -->
    <div class="pagination-info">
      <span class="has-text-grey">
        Page {{ currentPage }} of {{ totalPages }}
      </span>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
});

// Emits
const emit = defineEmits(['page-changed']);

// Computed
const visiblePages = computed(() => {
  const { currentPage, totalPages, maxVisiblePages } = props;
  const pages = [];
  
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
  
  // Adjust start page if we're near the end
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return pages;
});

const showFirstPage = computed(() => {
  return props.totalPages > 1 && !visiblePages.value.includes(1);
});

const showLastPage = computed(() => {
  return props.totalPages > 1 && !visiblePages.value.includes(props.totalPages);
});

const showFirstEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2;
});

const showLastEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1;
});

// Methods
const goToPage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-changed', page);
  }
};
</script>

<style scoped>
.pagination {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.pagination-previous,
.pagination-next,
.pagination-link {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.pagination-previous:hover,
.pagination-next:hover,
.pagination-link:hover {
  background-color: var(--darker-bg);
  border-color: var(--accent-color);
}

.pagination-link.is-current {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.pagination-previous:disabled,
.pagination-next:disabled {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
  color: var(--text-secondary);
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  color: var(--text-secondary);
}

.pagination-info {
  text-align: center;
  margin-top: 1rem;
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .pagination-previous,
  .pagination-next {
    font-size: 0.875rem;
  }
  
  .pagination-link {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
  
  .pagination-info {
    font-size: 0.875rem;
  }
}
</style>

<template>
  <div class="admin-security">
    <h1 class="title">Security & Logs</h1>
    
    <div class="tabs">
      <ul>
        <li :class="{ 'is-active': activeTab === 'logs' }">
          <a @click="activeTab = 'logs'">System Logs</a>
        </li>
        <li :class="{ 'is-active': activeTab === 'audit' }">
          <a @click="activeTab = 'audit'">Audit Trail</a>
        </li>
        <li :class="{ 'is-active': activeTab === 'sessions' }">
          <a @click="activeTab = 'sessions'">Active Sessions</a>
        </li>
        <li :class="{ 'is-active': activeTab === 'settings' }">
          <a @click="activeTab = 'settings'">Security Settings</a>
        </li>
      </ul>
    </div>
    
    <!-- System Logs -->
    <div v-if="activeTab === 'logs'" class="security-content">
      <div class="box">
        <h2 class="subtitle">System Logs</h2>
        
        <div class="field is-grouped">
          <div class="control">
            <div class="select">
              <select v-model="logFilters.level">
                <option value="">All Levels</option>
                <option value="INFO">Info</option>
                <option value="WARNING">Warning</option>
                <option value="ERROR">Error</option>
              </select>
            </div>
          </div>
          <div class="control">
            <div class="select">
              <select v-model="logFilters.source">
                <option value="">All Sources</option>
                <option value="Authentication">Authentication</option>
                <option value="Authorization">Authorization</option>
                <option value="Database">Database</option>
              </select>
            </div>
          </div>
          <div class="control">
            <button class="button is-primary" @click="loadLogs">Refresh</button>
          </div>
        </div>
        
        <div class="table-container">
          <table class="table is-fullwidth is-striped">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Level</th>
                <th>Source</th>
                <th>Message</th>
                <th>IP Address</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="log in logs" :key="log.id">
                <td>{{ formatDate(log.timestamp) }}</td>
                <td>
                  <span class="tag" :class="getLogLevelClass(log.level)">
                    {{ log.level }}
                  </span>
                </td>
                <td>{{ log.source }}</td>
                <td>{{ log.message }}</td>
                <td>{{ log.ipAddress }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Audit Trail -->
    <div v-if="activeTab === 'audit'" class="security-content">
      <div class="box">
        <h2 class="subtitle">Audit Trail</h2>
        
        <div class="table-container">
          <table class="table is-fullwidth is-striped">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Action</th>
                <th>User</th>
                <th>Target</th>
                <th>Details</th>
                <th>IP Address</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="entry in auditTrail" :key="entry.id">
                <td>{{ formatDate(entry.timestamp) }}</td>
                <td>
                  <span class="tag is-info">{{ entry.action }}</span>
                </td>
                <td>{{ entry.userName }}</td>
                <td>{{ entry.targetType }}</td>
                <td>{{ entry.details }}</td>
                <td>{{ entry.ipAddress }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Active Sessions -->
    <div v-if="activeTab === 'sessions'" class="security-content">
      <div class="box">
        <h2 class="subtitle">Active Sessions</h2>
        
        <div class="table-container">
          <table class="table is-fullwidth is-striped">
            <thead>
              <tr>
                <th>User</th>
                <th>IP Address</th>
                <th>User Agent</th>
                <th>Login Time</th>
                <th>Last Activity</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="session in activeSessions" :key="session.id">
                <td>{{ session.userName }}</td>
                <td>{{ session.ipAddress }}</td>
                <td class="is-size-7">{{ truncateUserAgent(session.userAgent) }}</td>
                <td>{{ formatDate(session.loginTime) }}</td>
                <td>{{ formatDate(session.lastActivity) }}</td>
                <td>
                  <button 
                    v-if="!session.isCurrentSession"
                    class="button is-small is-danger"
                    @click="terminateSession(session.id)">
                    Terminate
                  </button>
                  <span v-else class="tag is-success">Current</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Security Settings -->
    <div v-if="activeTab === 'settings'" class="security-content">
      <div class="box">
        <h2 class="subtitle">Security Settings</h2>
        
        <div class="field">
          <label class="label">Password Policy</label>
          <div class="field">
            <label class="checkbox">
              <input type="checkbox" v-model="securitySettings.passwordPolicy.requireUppercase">
              Require uppercase letters
            </label>
          </div>
          <div class="field">
            <label class="checkbox">
              <input type="checkbox" v-model="securitySettings.passwordPolicy.requireNumbers">
              Require numbers
            </label>
          </div>
          <div class="field">
            <label class="checkbox">
              <input type="checkbox" v-model="securitySettings.passwordPolicy.requireSpecialChars">
              Require special characters
            </label>
          </div>
        </div>
        
        <div class="field">
          <label class="label">Session Timeout (minutes)</label>
          <div class="control">
            <input class="input" type="number" v-model="securitySettings.sessionSettings.sessionTimeout">
          </div>
        </div>
        
        <div class="field">
          <label class="label">Rate Limiting</label>
          <div class="field">
            <label class="checkbox">
              <input type="checkbox" v-model="securitySettings.rateLimiting.enabled">
              Enable rate limiting
            </label>
          </div>
          <div class="field" v-if="securitySettings.rateLimiting.enabled">
            <label class="label">Requests per minute</label>
            <div class="control">
              <input class="input" type="number" v-model="securitySettings.rateLimiting.requestsPerMinute">
            </div>
          </div>
        </div>
        
        <div class="field is-grouped">
          <div class="control">
            <button class="button is-primary" @click="saveSecuritySettings">
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import api from '@/services/api';

// Active tab
const activeTab = ref('logs');

// Data
const logs = ref([]);
const auditTrail = ref([]);
const activeSessions = ref([]);
const securitySettings = ref({
  passwordPolicy: {
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: true
  },
  sessionSettings: {
    sessionTimeout: 30
  },
  rateLimiting: {
    enabled: true,
    requestsPerMinute: 60
  }
});

// Filters
const logFilters = ref({
  level: '',
  source: ''
});

// Methods
const loadLogs = async () => {
  try {
    const response = await api.get('/api/admin/security/logs', {
      params: logFilters.value
    });
    logs.value = response.data.data.logs;
  } catch (error) {
    console.error('Error loading logs:', error);
  }
};

const loadAuditTrail = async () => {
  try {
    const response = await api.get('/api/admin/security/audit-trail');
    auditTrail.value = response.data.data.auditEntries;
  } catch (error) {
    console.error('Error loading audit trail:', error);
  }
};

const loadActiveSessions = async () => {
  try {
    const response = await api.get('/api/admin/security/active-sessions');
    activeSessions.value = response.data.data;
  } catch (error) {
    console.error('Error loading active sessions:', error);
  }
};

const loadSecuritySettings = async () => {
  try {
    const response = await api.get('/api/admin/security/security-settings');
    securitySettings.value = response.data.data;
  } catch (error) {
    console.error('Error loading security settings:', error);
  }
};

const saveSecuritySettings = async () => {
  try {
    await api.put('/api/admin/security/security-settings', securitySettings.value);
    alert('Security settings saved successfully!');
  } catch (error) {
    console.error('Error saving security settings:', error);
    alert('Error saving security settings');
  }
};

const terminateSession = async (sessionId) => {
  if (confirm('Are you sure you want to terminate this session?')) {
    try {
      await api.delete(`/api/admin/security/active-sessions/${sessionId}`);
      await loadActiveSessions();
    } catch (error) {
      console.error('Error terminating session:', error);
    }
  }
};

const formatDate = (date) => {
  return new Date(date).toLocaleString();
};

const getLogLevelClass = (level) => {
  switch (level) {
    case 'ERROR': return 'is-danger';
    case 'WARNING': return 'is-warning';
    case 'INFO': return 'is-info';
    default: return '';
  }
};

const truncateUserAgent = (userAgent) => {
  return userAgent.length > 50 ? userAgent.substring(0, 50) + '...' : userAgent;
};

onMounted(() => {
  loadLogs();
  loadAuditTrail();
  loadActiveSessions();
  loadSecuritySettings();
});
</script>

<style scoped>
.admin-security {
  padding: 1rem;
}

.tabs a {
  color: #9ca3af;
}

.tabs li.is-active a {
  color: #f3f4f6;
  border-bottom-color: #3B82F6;
}

.security-content {
  margin-top: 1.5rem;
}

.table-container {
  max-height: 500px;
  overflow-y: auto;
}
</style>

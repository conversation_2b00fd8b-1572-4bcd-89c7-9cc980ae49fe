<template>
  <div class="admin-page-container">
    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading product data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-error-state">
      <AdminCard variant="danger" title="Error Loading Product">
        <div class="admin-error-content">
          <i class="fas fa-exclamation-triangle admin-error-icon"></i>
          <p class="admin-error-message">{{ error }}</p>
          <button class="admin-btn admin-btn-primary" @click="loadProduct">
            <i class="fas fa-redo"></i>
            Try Again
          </button>
        </div>
      </AdminCard>
    </div>

    <!-- Form Content -->
    <div v-else-if="isMounted" class="admin-page-content">
      <!-- Header -->
      <AdminProductHeader 
        :mode="isCreate ? 'create' : 'edit'"
        :product="formData"
        :title="isCreate ? 'Create New Product' : `Edit Product: ${formData.name || 'Untitled'}`"
        :subtitle="isCreate ? 'Add a new product to your catalog' : 'Update product information and settings'"
        :can-save="canSave"
        :can-create="canCreate"
        :saving="saving"
        :creating="saving && isCreate"
        @save="handleSubmit"
        @create="handleSubmit"
      />

      <!-- Form Content -->
      <form @submit.prevent="handleSubmit" class="admin-form">
        <div class="admin-form-grid">
          <!-- Basic Information Section -->
          <AdminFormSection 
            title="Basic Information" 
            description="Enter the core product details"
            variant="card"
            :required="true"
          >
            <div class="admin-form-row" v-if="formData && !loading">
              <div class="admin-form-col">
                <SimpleCompanySelector
                  :model-value="formData.companyId"
                  @update:model-value="(value) => formData.companyId = value"
                  label="Company"
                  placeholder="Select company..."
                  :required="true"
                  :error-message="errors.companyId || ''"
                  help-text="Select the company that owns this product"
                  @change="handleCompanyChange"
                />
              </div>
              <div class="admin-form-col">
                <SimpleCategorySelector
                  :model-value="formData.categoryId"
                  @update:model-value="(value) => formData.categoryId = value"
                  label="Category"
                  placeholder="Select category..."
                  :required="true"
                  :error-message="errors.categoryId || ''"
                  help-text="Choose the most appropriate category"
                  @change="handleCategoryChange"
                />
              </div>
            </div>

            <div class="admin-form-row" v-if="formData">
              <div class="admin-form-col">
                <label class="admin-form-label admin-form-label--required">Product Name</label>
                <input
                  v-model="formData.name"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.name }"
                  placeholder="Enter product name"
                  required
                  @input="handleNameChange"
                />
                <div v-if="errors.name" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.name }}
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Product Slug</label>
                <input
                  v-model="formData.slug"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.slug }"
                  placeholder="Auto-generated from name"
                  @input="handleSlugChange"
                />
                <div v-if="errors.slug" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.slug }}
                </div>
                <div class="admin-form-help">
                  Leave empty to auto-generate from product name
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Description</label>
                <textarea
                  v-model="formData.description"
                  class="admin-form-textarea"
                  :class="{ 'admin-form-textarea--error': errors.description }"
                  placeholder="Enter product description"
                  rows="4"
                ></textarea>
                <div v-if="errors.description" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.description }}
                </div>
              </div>
            </div>
          </AdminFormSection>

          <!-- Pricing & Inventory Section -->
          <AdminFormSection 
            title="Pricing & Inventory" 
            description="Set product pricing and stock information"
            variant="card"
          >
            <div class="admin-form-row">
              <div class="admin-form-col admin-form-col--half">
                <label class="admin-form-label admin-form-label--required">Price</label>
                <div class="admin-input-group">
                  <input
                    v-model="formData.priceAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    class="admin-form-input"
                    :class="{ 'admin-form-input--error': errors.priceAmount }"
                    placeholder="0.00"
                    required
                  />
                  <select
                    v-model="formData.priceCurrency"
                    class="admin-form-select admin-form-select--addon"
                  >
                    <option value="UAH">UAH</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>
                <div v-if="errors.priceAmount" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.priceAmount }}
                </div>
              </div>
              
              <div class="admin-form-col admin-form-col--half">
                <label class="admin-form-label">Stock Quantity</label>
                <input
                  v-model="formData.stock"
                  type="number"
                  min="0"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.stock }"
                  placeholder="0"
                />
                <div v-if="errors.stock" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.stock }}
                </div>
                <div class="admin-form-help">
                  Number of items available in stock
                </div>
              </div>
            </div>
          </AdminFormSection>

          <!-- Product Attributes Section -->
          <AdminFormSection 
            title="Product Attributes" 
            description="Add custom attributes like color, size, material, etc."
            variant="card"
          >
            <AdminProductAttributesEditor
              v-model="formData.attributes"
              :show-json-preview="false"
              @change="handleAttributesChange"
            />
          </AdminFormSection>

          <!-- Product Images Section -->
          <AdminFormSection 
            title="Product Images" 
            description="Upload and manage product images"
            variant="card"
          >
            <AdminImageUploader
              v-model="formData.images"
              title="Product Images"
              help-text="Upload high-quality images of your product. The first image will be used as the main product image."
              :multiple="true"
              :max-size="5 * 1024 * 1024"
              accepted-types="image/*"
              @upload="handleImageUpload"
              @remove="handleImageRemove"
              @error="handleImageError"
            />
          </AdminFormSection>

          <!-- SEO & Metadata Section -->
          <AdminFormSection 
            title="SEO & Metadata" 
            description="Optimize your product for search engines"
            variant="card"
          >
            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Meta Title</label>
                <input
                  v-model="formData.metaTitle"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.metaTitle }"
                  placeholder="SEO title for search engines"
                  maxlength="60"
                />
                <div class="admin-form-help">
                  <span :class="getTitleLengthClass(formData.metaTitle)">
                    {{ (formData.metaTitle || '').length }}/60 characters
                  </span>
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Meta Description</label>
                <textarea
                  v-model="formData.metaDescription"
                  class="admin-form-textarea"
                  :class="{ 'admin-form-textarea--error': errors.metaDescription }"
                  placeholder="Brief description for search engine results"
                  rows="3"
                  maxlength="160"
                ></textarea>
                <div class="admin-form-help">
                  <span :class="getDescriptionLengthClass(formData.metaDescription)">
                    {{ (formData.metaDescription || '').length }}/160 characters
                  </span>
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Meta Image URL</label>
                <input
                  v-model="formData.metaImage"
                  type="url"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.metaImage }"
                  placeholder="https://example.com/image.jpg"
                />
                <div class="admin-form-help">
                  Image to display when sharing on social media
                </div>
              </div>
            </div>
          </AdminFormSection>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AdminCard from '../common/AdminCard.vue';
import AdminFormSection from '../common/AdminFormSection.vue';
import AdminProductHeader from './AdminProductHeader.vue';
import SimpleCompanySelector from './SimpleCompanySelector.vue';
import SimpleCategorySelector from './SimpleCategorySelector.vue';
import AdminProductAttributesEditor from './AdminProductAttributesEditor.vue';
import AdminImageUploader from './AdminImageUploader.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  productId: {
    type: [String, Number],
    default: null
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['save', 'cancel', 'created', 'updated']);

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const errors = ref({});
const isMounted = ref(false);

// Form data - ініціалізуємо з безпечними значеннями
const formData = ref({
  name: '',
  slug: '',
  description: '',
  companyId: null,
  categoryId: null,
  priceAmount: null,
  priceCurrency: 'UAH',
  stock: 0,
  attributes: {},
  images: [],
  metaTitle: '',
  metaDescription: '',
  metaImage: '',
  status: 0 // Default to pending
});

// Переконуємося, що formData завжди має правильну структуру
const ensureFormDataIntegrity = () => {
  if (!formData.value) {
    formData.value = {};
  }

  // Переконуємося, що всі необхідні поля існують
  const defaults = {
    name: '',
    slug: '',
    description: '',
    companyId: null,
    categoryId: null,
    priceAmount: null,
    priceCurrency: 'UAH',
    stock: 0,
    attributes: {},
    images: [],
    metaTitle: '',
    metaDescription: '',
    metaImage: '',
    status: 0
  };

  Object.keys(defaults).forEach(key => {
    if (formData.value[key] === undefined) {
      formData.value[key] = defaults[key];
    }
  });
};

// Original data for comparison
const originalData = ref({});

// Computed
const currentProductId = computed(() => {
  return props.productId || route.params.id;
});

const canSave = computed(() => {
  // Додаємо захист від null/undefined
  if (!formData.value) return false;

  return formData.value.name &&
         formData.value.companyId &&
         formData.value.categoryId &&
         formData.value.priceAmount !== null &&
         !saving.value;
});

const canCreate = computed(() => {
  return canSave.value && props.isCreate;
});

const hasChanges = computed(() => {
  if (props.isCreate) return true;
  if (!formData.value || !originalData.value) return false;

  return JSON.stringify(formData.value) !== JSON.stringify(originalData.value);
});

// Methods
const loadProduct = async () => {
  try {
    // Check if component is still mounted
    if (!isMounted.value) {
      return;
    }

    // Переконуємося, що formData має правильну структуру
    ensureFormDataIntegrity();

    if (props.isCreate) {
      // Set default values for new product
      formData.value = {
        name: '',
        slug: '',
        description: '',
        companyId: null,
        categoryId: null,
        priceAmount: null,
        priceCurrency: 'UAH',
        stock: 0,
        attributes: {},
        images: [],
        metaTitle: '',
        metaDescription: '',
        metaImage: '',
        status: 0
      };
      originalData.value = { ...formData.value };

      // Переконуємося, що структура правильна після ініціалізації
      await nextTick();
      if (isMounted.value) {
        ensureFormDataIntegrity();
      }
      return;
    }

    // Завантажуємо існуючий продукт
    loading.value = true;
    error.value = null;

    const response = await productsService.getProductById(currentProductId.value);

    // Check if component is still mounted after async operation
    if (!isMounted.value) {
      return;
    }

    const product = response.data || response;

    formData.value = {
      ...product,
      priceAmount: product.priceAmount || null,
      priceCurrency: product.priceCurrency || 'UAH',
      stock: product.stock || 0,
      attributes: product.attributes || {},
      images: product.images || [],
      metaTitle: product.metaTitle || '',
      metaDescription: product.metaDescription || '',
      metaImage: product.metaImage || ''
    };

    originalData.value = { ...formData.value };

    // Переконуємося, що структура правильна після завантаження
    await nextTick();
    if (isMounted.value) {
      ensureFormDataIntegrity();
    }

  } catch (err) {
    console.error('Error loading product:', err);

    // Only update error state if component is still mounted
    if (isMounted.value) {
      error.value = err.message || 'Failed to load product';
      // У випадку помилки, переконуємося, що formData має правильну структуру
      ensureFormDataIntegrity();
    }
  } finally {
    // Only update loading state if component is still mounted
    if (isMounted.value) {
      loading.value = false;
    }
  }
};

const validateForm = () => {
  errors.value = {};

  if (!formData.value.name?.trim()) {
    errors.value.name = 'Product name is required';
  }

  if (!formData.value.companyId) {
    errors.value.companyId = 'Company selection is required';
  }

  if (!formData.value.categoryId) {
    errors.value.categoryId = 'Category selection is required';
  }

  if (!formData.value.priceAmount || formData.value.priceAmount <= 0) {
    errors.value.priceAmount = 'Valid price is required';
  }

  if (formData.value.slug && !/^[a-z0-9-]+$/.test(formData.value.slug)) {
    errors.value.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
  }

  return Object.keys(errors.value).length === 0;
};

const generateSlug = (name) => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    saving.value = true;

    // Auto-generate slug if empty
    if (!formData.value.slug && formData.value.name) {
      formData.value.slug = generateSlug(formData.value.name);
    }

    const productData = {
      ...formData.value,
      attributes: JSON.stringify(formData.value.attributes)
    };

    let result;
    if (props.isCreate) {
      result = await productsService.createProduct(productData);
      emit('created', result);

      // Redirect to edit page
      const productId = result.data?.id || result.id;
      router.push(`/admin/products/${productId}/edit`);
    } else {
      result = await productsService.updateProduct(currentProductId.value, productData);
      emit('updated', result);
      originalData.value = { ...formData.value };
    }

    emit('save', result);
  } catch (err) {
    console.error('Error saving product:', err);
    error.value = err.message || 'Failed to save product';
  } finally {
    saving.value = false;
  }
};

// Event handlers
const handleNameChange = () => {
  if (!formData.value) {
    console.error('FormData not initialized when handling name change');
    return;
  }

  // Auto-generate slug if it's empty or matches the previous auto-generated slug
  if (!formData.value.slug || formData.value.slug === generateSlug(originalData.value?.name || '')) {
    formData.value.slug = generateSlug(formData.value.name || '');
  }

  // Auto-generate meta title if empty
  if (!formData.value.metaTitle) {
    formData.value.metaTitle = formData.value.name || '';
  }

  // Clear name error
  if (errors.value && errors.value.name) {
    delete errors.value.name;
  }
};

const handleSlugChange = () => {
  // Clear slug error
  if (errors.value.slug) {
    delete errors.value.slug;
  }
};

const handleCompanyChange = (companyId) => {
  if (!formData.value) {
    console.error('FormData not initialized when handling company change');
    return;
  }

  formData.value.companyId = companyId;
  if (errors.value && errors.value.companyId) {
    delete errors.value.companyId;
  }
};

const handleCategoryChange = (categoryId) => {
  if (!formData.value) {
    console.error('FormData not initialized when handling category change');
    return;
  }

  formData.value.categoryId = categoryId;
  if (errors.value && errors.value.categoryId) {
    delete errors.value.categoryId;
  }
};

const handleAttributesChange = (attributes) => {
  formData.value.attributes = attributes;
};

const handleImageUpload = (files) => {
  // Handle image upload logic here
  console.log('Uploading images:', files);
  // This would typically upload to a server and update the images array
};

const handleImageRemove = (index) => {
  formData.value.images.splice(index, 1);
};

const handleImageError = (error) => {
  console.error('Image error:', error);
  // Show error message to user
};

// Helper methods for SEO character counts
const getTitleLengthClass = (title) => {
  const length = (title || '').length;
  if (length <= 50) return 'admin-text-success';
  if (length <= 60) return 'admin-text-warning';
  return 'admin-text-danger';
};

const getDescriptionLengthClass = (description) => {
  const length = (description || '').length;
  if (length <= 140) return 'admin-text-success';
  if (length <= 160) return 'admin-text-warning';
  return 'admin-text-danger';
};

// Lifecycle
onMounted(async () => {
  try {
    isMounted.value = true;

    // Переконуємося, що formData має правильну структуру з самого початку
    ensureFormDataIntegrity();

    // Ensure DOM is ready before loading data
    await nextTick();

    // Check if component is still mounted before proceeding
    if (!isMounted.value) return;

    await loadProduct();

    // Add beforeunload listener
    window.addEventListener('beforeunload', beforeUnload);
  } catch (error) {
    console.error('Error in ProductEdit onMounted:', error);
    // У випадку помилки, переконуємося, що formData має правильну структуру
    if (isMounted.value) {
      ensureFormDataIntegrity();
    }
  }
});

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId && !props.isCreate && isMounted.value) {
    await nextTick();
    await loadProduct();
  }
});

// Warn about unsaved changes
const beforeUnload = (event) => {
  if (hasChanges.value && !saving.value) {
    event.preventDefault();
    event.returnValue = '';
  }
};

onBeforeUnmount(() => {
  isMounted.value = false;
});

onUnmounted(() => {
  window.removeEventListener('beforeunload', beforeUnload);
});
</script>

<style scoped>
.admin-page-container {
  min-height: 100vh;
  background: var(--admin-bg-secondary);
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.admin-loading-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-lg);
  color: var(--admin-text-muted);
  margin: 0;
}

.admin-error-state {
  max-width: 600px;
  margin: var(--admin-space-xl) auto;
}

.admin-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--admin-space-md);
}

.admin-error-icon {
  font-size: 3rem;
  color: var(--admin-danger);
}

.admin-error-message {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin: 0;
  line-height: 1.6;
}

.admin-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--admin-space-lg);
}

.admin-form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.admin-form-row {
  display: flex;
  gap: var(--admin-space-lg);
  align-items: flex-start;
}

.admin-form-col {
  flex: 1;
  min-width: 0;
}

.admin-form-col--half {
  flex: 0 0 calc(50% - var(--admin-space-lg) / 2);
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-form-input,
.admin-form-textarea,
.admin-form-select {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-input:focus,
.admin-form-textarea:focus,
.admin-form-select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-input--error,
.admin-form-textarea--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-textarea {
  resize: vertical;
  min-height: 100px;
}

.admin-input-group {
  display: flex;
  gap: 0;
}

.admin-input-group .admin-form-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.admin-form-select--addon {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  min-width: 80px;
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-danger);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

.admin-text-success {
  color: var(--admin-success);
}

.admin-text-warning {
  color: var(--admin-warning);
}

.admin-text-danger {
  color: var(--admin-danger);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-form {
    padding: 0 var(--admin-space-md);
  }

  .admin-form-grid {
    gap: var(--admin-space-lg);
  }

  .admin-form-row {
    flex-direction: column;
    gap: var(--admin-space-md);
  }

  .admin-form-col--half {
    flex: 1;
  }

  .admin-input-group {
    flex-direction: column;
  }

  .admin-input-group .admin-form-input {
    border-radius: var(--admin-radius-md);
    border-right: 1px solid var(--admin-border-light);
  }

  .admin-form-select--addon {
    border-radius: var(--admin-radius-md);
  }
}
</style>

<template>
  <div class="admin-category-selector">
    <label v-if="label" class="admin-form-label" :class="{ 'admin-form-label--required': required }">
      {{ label }}
    </label>
    
    <div class="admin-selector-container" :class="{ 'admin-selector-container--error': hasError }">
      <!-- Search Input -->
      <div class="admin-search-input-wrapper">
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          class="admin-search-input"
          :placeholder="placeholder"
          :disabled="disabled || loading"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeydown"
          autocomplete="off"
        />
        <div class="admin-search-input-icons">
          <i v-if="loading" class="fas fa-spinner fa-spin admin-search-icon"></i>
          <i v-else-if="selectedCategory && selectedCategory.id" class="fas fa-tag admin-search-icon"></i>
          <i v-else class="fas fa-search admin-search-icon"></i>

          <button
            v-if="selectedCategory && selectedCategory.id && !disabled"
            type="button"
            class="admin-clear-btn"
            @click="clearSelection"
            title="Clear selection"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <!-- Dropdown -->
      <div v-if="showDropdown && isReady" class="admin-dropdown">
        <div v-if="loading" class="admin-dropdown-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Loading categories...</span>
        </div>
        
        <div v-else-if="!filteredCategories || filteredCategories.length === 0" class="admin-dropdown-empty">
          <i class="fas fa-tag"></i>
          <span>{{ searchQuery ? 'No categories found' : 'No categories available' }}</span>
        </div>

        <div v-else-if="filteredCategories && filteredCategories.length > 0" class="admin-dropdown-list">
          <button
            v-for="(category, index) in filteredCategories"
            v-if="category && category.id"
            :key="category.id"
            type="button"
            class="admin-dropdown-item"
            :class="{ 'admin-dropdown-item--highlighted': index === highlightedIndex }"
            @click="selectCategory(category)"
            @mouseenter="highlightedIndex = index"
          >
            <div class="admin-category-item">
              <div class="admin-category-info">
                <div class="admin-category-name">{{ category.name || 'Unknown Category' }}</div>
                <div v-if="category.slug" class="admin-category-slug">{{ category.slug }}</div>
                <div v-if="category.parentId" class="admin-category-parent">
                  <i class="fas fa-arrow-up"></i>
                  Parent category
                </div>
              </div>
              <div class="admin-category-icon">
                <i class="fas fa-tag"></i>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Selected Category Display -->
    <div v-if="selectedCategory && !showDropdown" class="admin-selected-category">
      <div class="admin-category-card">
        <div class="admin-category-avatar">
          <i class="fas fa-tag"></i>
        </div>
        <div class="admin-category-details">
          <div class="admin-category-name">{{ selectedCategory.name }}</div>
          <div class="admin-category-meta">
            <span v-if="selectedCategory.slug" class="admin-category-slug">{{ selectedCategory.slug }}</span>
            <span v-if="selectedCategory.parentId" class="admin-category-type">
              <i class="fas fa-arrow-up"></i>
              Subcategory
            </span>
            <span v-else class="admin-category-type">
              <i class="fas fa-folder"></i>
              Main Category
            </span>
          </div>
        </div>
        <button
          v-if="!disabled"
          type="button"
          class="admin-category-remove"
          @click="clearSelection"
          title="Remove category"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
    
    <!-- Error Message -->
    <div v-if="errorMessage" class="admin-form-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ errorMessage }}
    </div>
    
    <!-- Help Text -->
    <div v-if="helpText" class="admin-form-help">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: 'Category'
  },
  placeholder: {
    type: String,
    default: 'Search for a category...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// Reactive data
const searchQuery = ref('');
const categories = ref([]);
const loading = ref(false);
const showDropdown = ref(false);
const highlightedIndex = ref(-1);
const searchInput = ref(null);

// AbortController for request cancellation
let abortController = null;

// Computed
const hasError = computed(() => !!props.errorMessage);

// Додаємо обчислювану властивість для перевірки готовності
const isReady = computed(() => {
  return !loading.value && Array.isArray(categories.value);
});

const selectedCategory = computed(() => {
  if (!props.modelValue || !isReady.value) return null;
  const categoriesList = categories.value || [];
  return categoriesList.find(category => category && category.id === props.modelValue);
});

const filteredCategories = computed(() => {
  // Ensure categories.value is always an array and component is ready
  if (!isReady.value) return [];
  const categoriesList = categories.value || [];

  if (!searchQuery.value.trim()) return categoriesList;

  const query = searchQuery.value.toLowerCase();
  return categoriesList.filter(category =>
    category && category.name && (
      category.name.toLowerCase().includes(query) ||
      (category.slug && category.slug.toLowerCase().includes(query))
    )
  );
});

// Methods
const loadCategories = async () => {
  try {
    // Cancel previous request if exists
    if (abortController) {
      abortController.abort();
    }

    // Create new AbortController
    abortController = new AbortController();

    loading.value = true;
    const response = await productsService.getCategories({
      pageSize: 1000,
      signal: abortController.signal
    });

    // Ensure response is always an array
    if (Array.isArray(response)) {
      categories.value = response;
    } else if (response && Array.isArray(response.data)) {
      categories.value = response.data;
    } else {
      categories.value = [];
    }
  } catch (error) {
    // Don't log canceled requests as errors
    if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
      console.log('Categories request was canceled');
      return;
    }

    console.error('Error loading categories:', error);
    categories.value = [];
  } finally {
    loading.value = false;
    abortController = null;
  }
};

const selectCategory = (category) => {
  if (!category || !category.id) {
    console.error('Invalid category selected:', category);
    return;
  }

  emit('update:modelValue', category.id);
  emit('change', category.id);
  emit('select', category);

  searchQuery.value = category.name || '';
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const clearSelection = () => {
  emit('update:modelValue', null);
  emit('change', null);
  emit('select', null);
  
  searchQuery.value = '';
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const handleFocus = () => {
  showDropdown.value = true;
  if (selectedCategory.value) {
    searchQuery.value = '';
  }
};

const handleBlur = () => {
  // Delay hiding dropdown to allow for clicks
  setTimeout(() => {
    showDropdown.value = false;
    if (selectedCategory.value) {
      searchQuery.value = selectedCategory.value.name;
    }
  }, 200);
};

const handleKeydown = (event) => {
  if (!showDropdown.value) return;
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredCategories.value.length - 1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (highlightedIndex.value >= 0 && filteredCategories.value[highlightedIndex.value]) {
        selectCategory(filteredCategories.value[highlightedIndex.value]);
      }
      break;
    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      searchInput.value?.blur();
      break;
  }
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  if (newValue && selectedCategory.value && selectedCategory.value.name) {
    searchQuery.value = selectedCategory.value.name;
  } else {
    searchQuery.value = '';
  }
}, { immediate: false });

watch(searchQuery, () => {
  highlightedIndex.value = -1;
});

// Lifecycle
onMounted(async () => {
  try {
    await nextTick(); // Ensure DOM is ready
    await loadCategories();

    // Set initial search query if there's a selected category
    await nextTick(); // Wait for categories to be loaded
    if (props.modelValue && selectedCategory.value && selectedCategory.value.name) {
      searchQuery.value = selectedCategory.value.name;
    }
  } catch (error) {
    console.error('Error in AdminCategorySelector onMounted:', error);
  }
});

// Cleanup on unmount
onUnmounted(() => {
  if (abortController) {
    abortController.abort();
    abortController = null;
  }
});
</script>

<style scoped>
.admin-category-selector {
  position: relative;
  width: 100%;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-selector-container {
  position: relative;
}

.admin-selector-container--error .admin-search-input {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-search-input-wrapper {
  position: relative;
}

.admin-search-input {
  width: 100%;
  padding: var(--admin-space-sm) var(--admin-space-xl) var(--admin-space-sm) var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-search-input:disabled {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
  cursor: not-allowed;
}

.admin-search-input-icons {
  position: absolute;
  right: var(--admin-space-sm);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-search-icon {
  color: var(--admin-text-muted);
  font-size: var(--admin-text-sm);
}

.admin-clear-btn {
  background: none;
  border: none;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-clear-btn:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-danger);
}

/* Dropdown */
.admin-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  box-shadow: var(--admin-shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  margin-top: var(--admin-space-xs);
}

.admin-dropdown-loading,
.admin-dropdown-empty {
  padding: var(--admin-space-lg);
  text-align: center;
  color: var(--admin-text-muted);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-dropdown-list {
  padding: var(--admin-space-xs);
}

.admin-dropdown-item {
  width: 100%;
  padding: var(--admin-space-sm);
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  border-radius: var(--admin-radius-sm);
  transition: background-color var(--admin-transition-base);
}

.admin-dropdown-item:hover,
.admin-dropdown-item--highlighted {
  background: var(--admin-bg-secondary);
}

.admin-category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--admin-space-sm);
}

.admin-category-info {
  flex: 1;
  min-width: 0;
}

.admin-category-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-category-slug {
  font-size: var(--admin-text-xs);
  color: var(--admin-text-muted);
  font-family: var(--admin-font-mono);
  margin-bottom: var(--admin-space-xs);
}

.admin-category-parent {
  font-size: var(--admin-text-xs);
  color: var(--admin-info);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-category-icon {
  flex-shrink: 0;
  color: var(--admin-primary);
}

/* Selected Category */
.admin-selected-category {
  margin-top: var(--admin-space-sm);
}

.admin-category-card {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-space-sm);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-category-avatar {
  width: 40px;
  height: 40px;
  background: var(--admin-primary);
  color: var(--admin-text-white);
  border-radius: var(--admin-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--admin-text-lg);
  flex-shrink: 0;
}

.admin-category-details {
  flex: 1;
  min-width: 0;
}

.admin-category-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  margin-top: var(--admin-space-xs);
}

.admin-category-type {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-xs);
  color: var(--admin-text-muted);
}

.admin-category-remove {
  background: none;
  border: none;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  flex-shrink: 0;
}

.admin-category-remove:hover {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

/* Form Messages */
.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-danger);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-category-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-xs);
  }
  
  .admin-category-icon {
    align-self: flex-start;
  }
}
</style>

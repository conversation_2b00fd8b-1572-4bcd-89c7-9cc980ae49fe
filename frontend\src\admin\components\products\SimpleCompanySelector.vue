<template>
  <div class="simple-company-selector">
    <label v-if="label" class="admin-form-label" :class="{ 'admin-form-label--required': required }">
      {{ label }}
    </label>
    
    <div class="admin-selector-container" :class="{ 'admin-selector-container--error': hasError }">
      <select
        :value="modelValue"
        @change="handleChange"
        class="admin-form-select"
        :class="{ 'admin-form-select--error': hasError }"
        :disabled="disabled || loading"
        :required="required"
      >
        <option value="">{{ placeholder || 'Select company...' }}</option>
        <option
          v-for="company in companies"
          :key="company.id"
          :value="company.id"
        >
          {{ company.name }} {{ company.isApproved ? '✓' : '⏳' }}
        </option>
      </select>
      
      <div v-if="loading" class="admin-selector-loading">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
    </div>
    
    <div v-if="errorMessage" class="admin-form-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ errorMessage }}
    </div>
    
    <div v-if="helpText" class="admin-form-help">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Select company...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// State
const companies = ref([]);
const loading = ref(false);

// Computed
const hasError = computed(() => !!props.errorMessage);

// Methods
const handleChange = (event) => {
  const value = event.target.value || null;
  emit('update:modelValue', value);
  emit('change', value);
  
  if (value) {
    const selectedCompany = companies.value.find(c => c.id === value);
    emit('select', selectedCompany);
  } else {
    emit('select', null);
  }
};

const loadCompanies = async () => {
  try {
    loading.value = true;
    const response = await productsService.getCompanies({ pageSize: 1000 });
    
    if (Array.isArray(response)) {
      companies.value = response;
    } else if (response && Array.isArray(response.data)) {
      companies.value = response.data;
    } else {
      companies.value = [];
    }
  } catch (error) {
    console.error('Error loading companies:', error);
    companies.value = [];
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  loadCompanies();
});
</script>

<style scoped>
.simple-company-selector {
  width: 100%;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-selector-container {
  position: relative;
}

.admin-form-select {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-select--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-select:disabled {
  background: var(--admin-bg-disabled);
  color: var(--admin-text-disabled);
  cursor: not-allowed;
}

.admin-selector-loading {
  position: absolute;
  right: var(--admin-space-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-primary);
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  color: var(--admin-danger);
  font-size: var(--admin-text-sm);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
}
</style>

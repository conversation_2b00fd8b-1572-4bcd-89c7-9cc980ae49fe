<template>
  <div class="admin-order-view">
    <!-- Header -->
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <nav class="breadcrumb" aria-label="breadcrumbs">
            <ul>
              <li>
                <router-link to="/admin/orders">
                  <span class="icon is-small">
                    <i class="fas fa-list"></i>
                  </span>
                  <span>Orders</span>
                </router-link>
              </li>
              <li class="is-active">
                <a href="#" aria-current="page">
                  <span class="icon is-small">
                    <i class="fas fa-eye"></i>
                  </span>
                  <span>View Order</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <router-link 
              :to="`/admin/orders/${orderId}/edit`" 
              class="button is-primary"
              v-if="order"
            >
              <span class="icon">
                <i class="fas fa-edit"></i>
              </span>
              <span>Edit Order</span>
            </router-link>
            <button 
              class="button is-light"
              @click="goBack"
            >
              <span class="icon">
                <i class="fas fa-arrow-left"></i>
              </span>
              <span>Back to Orders</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="has-text-centered py-6" v-if="loading">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading order details...</p>
    </div>

    <!-- Error State -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchOrderDetails">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Order Not Found -->
    <div class="notification is-warning" v-else-if="!order">
      <p>Order not found.</p>
      <button class="button is-light mt-2" @click="goBack">
        <span class="icon"><i class="fas fa-arrow-left"></i></span>
        <span>Back to Orders</span>
      </button>
    </div>

    <!-- Order Content -->
    <div v-else>
      <!-- Order Header Info -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <div>
                  <h1 class="title is-4">
                    Order #{{ order.id }}
                  </h1>
                  <p class="subtitle is-6">
                    Placed on {{ formatDate(order.createdAt) }} at {{ formatTime(order.createdAt) }}
                  </p>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="tags">
                  <span class="tag is-medium" :class="getOrderStatusClass(order.status)">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                  <span class="tag is-medium" :class="getPaymentStatusClass(order.paymentStatus)">
                    {{ getPaymentStatusText(order.paymentStatus) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Details Component -->
      <OrderDetails :order="order" />

      <!-- Order Items Table Component -->
      <div class="mt-4">
        <OrderItemsTable
          :items="order.items || []"
          :tax="0"
          :shipping="order.shipping || 0"
          :discount="order.discount || 0"
          :editable="false"
        />
      </div>

      <!-- Order Status Update Component -->
      <div class="mt-4">
        <OrderStatusUpdate
          :order-id="order.id"
          :current-order-status="order.status"
          :current-payment-status="order.paymentStatus"
          :customer-name="order.customerName"
          :status-history="order.statusHistory || []"
          :loading="updating"
          @update-order-status="handleUpdateOrderStatus"
          @update-payment-status="handleUpdatePaymentStatus"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import OrderDetails from '@/admin/components/orders/OrderDetails.vue';
import OrderItemsTable from '@/admin/components/orders/OrderItemsTable.vue';
import OrderStatusUpdate from '@/admin/components/orders/OrderStatusUpdate.vue';
import { ordersService } from '@/admin/services/orders';
import {
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass
} from '@/admin/utils/orderConstants';

// Router
const route = useRoute();
const router = useRouter();

// State
const order = ref(null);
const loading = ref(false);
const updating = ref(false);
const error = ref(null);

// Computed
const orderId = computed(() => route.params.id);

// Methods
const fetchOrderDetails = async () => {
  if (!orderId.value) {
    error.value = 'Order ID is required';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    console.log('Fetching order details for ID:', orderId.value);
    const orderData = await ordersService.getOrderById(orderId.value);
    
    if (orderData) {
      order.value = orderData;
      console.log('Order details loaded:', orderData);
    } else {
      error.value = 'Order not found';
    }
  } catch (err) {
    console.error('Error fetching order details:', err);
    error.value = err.message || 'Failed to load order details';
  } finally {
    loading.value = false;
  }
};

const handleUpdateOrderStatus = async (orderId, newStatus) => {
  updating.value = true;
  try {
    await ordersService.updateOrderStatus(orderId, newStatus);
    
    // Update local order status
    if (order.value) {
      order.value.status = newStatus;
    }
    
    console.log('Order status updated successfully');
  } catch (err) {
    console.error('Error updating order status:', err);
    error.value = 'Failed to update order status';
  } finally {
    updating.value = false;
  }
};

const handleUpdatePaymentStatus = async (orderId, newStatus) => {
  updating.value = true;
  try {
    await ordersService.updatePaymentStatus(orderId, newStatus);
    
    // Update local payment status
    if (order.value) {
      order.value.paymentStatus = newStatus;
    }
    
    console.log('Payment status updated successfully');
  } catch (err) {
    console.error('Error updating payment status:', err);
    error.value = 'Failed to update payment status';
  } finally {
    updating.value = false;
  }
};

const goBack = () => {
  router.push('/admin/orders');
};

// Utility functions
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Status utility functions are now imported from orderConstants

// Lifecycle
onMounted(() => {
  fetchOrderDetails();
});
</script>

<style scoped>
.admin-order-view {
  padding: 1rem;
}

.breadcrumb {
  margin-bottom: 0;
}

.breadcrumb a {
  display: flex;
  align-items: center;
}

.breadcrumb .icon {
  margin-right: 0.25rem;
}

.level {
  margin-bottom: 1.5rem;
}

.tags {
  gap: 0.5rem;
}

.tag.is-medium {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.card {
  box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
}
</style>

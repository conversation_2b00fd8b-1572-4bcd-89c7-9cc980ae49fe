<template>
  <div class="admin-dashboard">
    <div class="admin-page-header">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <h1 class="admin-page-title">Dashboard</h1>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <button class="admin-btn admin-btn-secondary" @click="fetchDashboardData" :disabled="loading">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div class="admin-alert admin-alert-danger" v-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Loading Indicator -->
    <div class="admin-loading" v-if="loading && !stats.products">
      <i class="fas fa-spinner fa-pulse"></i>
      <div class="admin-loading-text">Loading dashboard data...</div>
      <div class="admin-loading-subtext">This may take a moment to fetch data from the database</div>
      <div class="mt-4" v-if="loadingTime > 10">
        <button class="admin-btn admin-btn-warning" @click="cancelAndRetry">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Taking too long? Click to retry</span>
        </button>
      </div>
    </div>

    <div v-else-if="noData">
      <div class="admin-alert admin-alert-warning">
        <div class="admin-alert-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="admin-alert-content">
          <div class="admin-alert-title">No data available</div>
          <div class="admin-alert-message">The database may be empty or there was an error retrieving the data.</div>
          <button class="admin-btn admin-btn-sm admin-btn-warning mt-2" @click="fetchDashboardData">
            <i class="fas fa-sync-alt"></i>
            <span>Try Again</span>
          </button>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- Stats Cards -->
      <div class="columns is-multiline">
        <div class="column is-one-fifth">
          <admin-stat-card
            title="Products"
            :value="stats.products"
            icon="box"
            color="is-info" />
        </div>
        <div class="column is-one-fifth">
          <admin-stat-card
            title="Users"
            :value="stats.users"
            icon="users"
            color="is-success" />
        </div>
        <div class="column is-one-fifth">
          <admin-stat-card
            title="Orders"
            :value="stats.orders"
            icon="shopping-cart"
            color="is-warning" />
        </div>
        <div class="column is-one-fifth">
          <admin-stat-card
            title="Revenue"
            :value="formatCurrency(stats.revenue)"
            icon="hryvnia"
            color="is-primary" />
        </div>
        <div class="column is-one-fifth">
          <admin-stat-card
            title="Site Profit"
            :value="formatCurrency(siteProfit)"
            icon="chart-line"
            color="is-link" />
        </div>
      </div>

      <!-- Charts -->
      <div class="columns">
        <div class="column is-8">
          <sales-chart
            :data="salesData"
            @period-changed="handlePeriodChange" />
        </div>
        <div class="column is-4">
          <orders-by-status-chart :data="ordersByStatus" />
        </div>
      </div>

      <!-- Site Profit Chart -->
      <div class="columns">
        <div class="column is-12">
          <site-profit-chart
            :data="siteProfitData"
            :total-profit="siteProfitForPeriod"
            @period-changed="handleProfitPeriodChange" />
        </div>
      </div>

      <!-- Quick Actions Card -->
      <div class="columns">
        <div class="column is-12">
          <div class="admin-card admin-quick-actions">
            <div class="admin-card-header">
              <h3 class="admin-card-title">
                <i class="fas fa-bolt"></i>
                Quick Actions
              </h3>
            </div>
            <div class="admin-card-content">
              <div class="admin-quick-actions-grid">
                <router-link to="/admin/products/create" class="admin-btn admin-btn-primary admin-quick-action">
                  <i class="fas fa-plus"></i>
                  <span>Add Product</span>
                </router-link>
                <router-link to="/admin/categories/create" class="admin-btn admin-btn-success admin-quick-action">
                  <i class="fas fa-folder-plus"></i>
                  <span>Add Category</span>
                </router-link>
                <router-link to="/admin/users" class="admin-btn admin-btn-warning admin-quick-action">
                  <i class="fas fa-user-cog"></i>
                  <span>Manage Users</span>
                </router-link>
                <router-link to="/admin/orders" class="admin-btn admin-btn-info admin-quick-action">
                  <i class="fas fa-shipping-fast"></i>
                  <span>Process Orders</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="columns">
        <div class="column is-6">
          <recent-orders :orders="recentOrders" />
        </div>
        <div class="column is-6">
          <pending-seller-requests :requests="sellerRequests" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import AdminStatCard from '@/admin/components/common/AdminStatCard.vue';
import SalesChart from '@/admin/components/dashboard/SalesChart.vue';
import OrdersByStatusChart from '@/admin/components/dashboard/OrdersByStatusChart.vue';
import SiteProfitChart from '@/admin/components/dashboard/SiteProfitChart.vue';
import RecentOrders from '@/admin/components/dashboard/RecentOrders.vue';
import PendingSellerRequests from '@/admin/components/dashboard/PendingSellerRequests.vue';
import { dashboardService } from '@/admin/services/dashboard';

// Loading state
const loading = ref(true);
const error = ref(null);
const loadingTime = ref(0);
const loadingTimer = ref(null);

// Check if we have no data
const noData = computed(() => {
  return !loading.value &&
         stats.value.products === 0 &&
         stats.value.users === 0 &&
         stats.value.orders === 0 &&
         stats.value.revenue === 0 &&
         salesData.value.length === 0 &&
         ordersByStatus.value.length === 0;
});

// Calculate site profit (15% of total revenue)
const siteProfit = computed(() => {
  const revenue = typeof stats.value.revenue === 'string' ? Number(stats.value.revenue) : stats.value.revenue;
  return isNaN(revenue) ? 0 : revenue * 0.15;
});

// Calculate site profit for current period
const siteProfitForPeriod = computed(() => {
  if (!siteProfitData.value || siteProfitData.value.length === 0) return 0;
  return siteProfitData.value.reduce((sum, item) => sum + (item.value || 0), 0);
});

// Stats data
const stats = ref({
  products: 0,
  users: 0,
  orders: 0,
  revenue: 0
});

// Chart data
const salesData = ref([]);
const ordersByStatus = ref([]);
const siteProfitData = ref([]);
const selectedPeriod = ref('week'); // Changed default to week
const selectedProfitPeriod = ref('week'); // Changed default to week

// Table data
const recentOrders = ref([]);
const sellerRequests = ref([]);

// Format currency
const formatCurrency = (value) => {
  // Ensure value is a number
  const numValue = typeof value === 'string' ? Number(value) : value;

  // Check if value is a valid number
  if (isNaN(numValue)) {
    console.error('Invalid currency value:', value);
    return 'UAH 0.00';
  }

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    currencyDisplay: 'code'
  }).format(numValue).replace('UAH', 'UAH');
};

// Computed properties
const hasSellerRequests = computed(() => sellerRequests.value.length > 0);
const pendingSellerRequestsCount = computed(() => sellerRequests.value.length);

// Fetch dashboard data with debounce
let fetchDataTimeout = null;
const fetchDashboardData = async () => {
  // Clear any existing timeout
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
  }

  // Clear any existing loading timer
  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }

  // Reset loading state
  loading.value = true;
  error.value = null;
  loadingTime.value = 0;

  // Start loading timer
  loadingTimer.value = setInterval(() => {
    loadingTime.value++;
  }, 1000);

  // Use a small timeout to debounce rapid calls
  fetchDataTimeout = setTimeout(async () => {
    try {
      console.log('Fetching dashboard data...');
      const data = await dashboardService.getDashboardData();

      // Log the data for debugging
      console.log('Dashboard data received:', data);

      // Validate data structure and provide detailed logging
      console.log('Data structure check:', {
        hasData: !!data,
        dataType: typeof data,
        keys: data ? Object.keys(data) : 'none'
      });

      if (!data) {
        throw new Error('No data received from server');
      }

      // Handle different response structures
      if (data.stats) {
        // Direct structure
        stats.value = {
          products: data.stats.products || 0,
          users: data.stats.users || 0,
          orders: data.stats.orders || 0,
          revenue: Number(data.stats.revenue) || 0
        };
        console.log('Revenue value:', stats.value.revenue, typeof stats.value.revenue);

        // Force revenue to be a number and log it for debugging
        if (typeof stats.value.revenue !== 'number' || isNaN(stats.value.revenue)) {
          console.warn('Revenue is not a valid number, converting:', data.stats.revenue);
          stats.value.revenue = 0;

          // Try to parse the revenue if it's a string
          if (typeof data.stats.revenue === 'string') {
            const parsed = parseFloat(data.stats.revenue.replace(/[^\d.-]/g, ''));
            if (!isNaN(parsed)) {
              stats.value.revenue = parsed;
              console.log('Parsed revenue:', parsed);
            }
          }
        }

        salesData.value = data.salesData || [];
        ordersByStatus.value = data.ordersByStatus || [];
        recentOrders.value = data.recentOrders || [];
        sellerRequests.value = data.sellerRequests || [];

        // Load initial chart data with default week period
        try {
          const weekSalesData = await dashboardService.getSalesData('week');
          salesData.value = weekSalesData;

          const weekProfitData = await dashboardService.getSiteProfitData('week');
          siteProfitData.value = weekProfitData;
        } catch (chartError) {
          console.warn('Error loading initial chart data:', chartError);
          // Fallback to calculating profit from sales data
          siteProfitData.value = (data.salesData || []).map(item => ({
            ...item,
            value: item.value * 0.15
          }));
        }
      } else {
        // If data is the entire response object
        console.log('Trying alternative data structure');
        stats.value = {
          products: data.products || 0,
          users: data.users || 0,
          orders: data.orders || 0,
          revenue: Number(data.revenue) || 0
        };
        console.log('Revenue value (alt):', stats.value.revenue, typeof stats.value.revenue);

        salesData.value = [];
        ordersByStatus.value = [];
        recentOrders.value = [];
        sellerRequests.value = [];
        siteProfitData.value = [];
      }

      // Check if we received empty data
      if (noData.value) {
        console.warn('Dashboard data is empty');
      }
    } catch (err) {
      // Only show error if it's not a cancelled request
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching dashboard data:', err);
        error.value = `Failed to load dashboard data: ${err.message}. Please try again later.`;
      }
    } finally {
      // Stop loading timer
      if (loadingTimer.value) {
        clearInterval(loadingTimer.value);
        loadingTimer.value = null;
      }

      loading.value = false;
      fetchDataTimeout = null;
    }
  }, 100); // Small delay to prevent multiple rapid calls
};

// Cancel current request and retry
const cancelAndRetry = () => {
  // Cancel any pending requests
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
    fetchDataTimeout = null;
  }

  // Stop loading timer
  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }

  // Reset loading state
  loading.value = false;
  loadingTime.value = 0;

  // Retry after a short delay
  setTimeout(() => {
    fetchDashboardData();
  }, 500);
};

// Handle period change for sales chart with debounce
let periodChangeTimeout = null;
const handlePeriodChange = async (period) => {
  // Clear any existing timeout
  if (periodChangeTimeout) {
    clearTimeout(periodChangeTimeout);
  }

  selectedPeriod.value = period;

  // Use a small timeout to debounce rapid calls
  periodChangeTimeout = setTimeout(async () => {
    try {
      const data = await dashboardService.getSalesData(period);
      salesData.value = data;
    } catch (err) {
      // Only show error if it's not a cancelled request
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching sales data:', err);
        error.value = err.message || `Failed to load sales data for period: ${period}`;
      }
    } finally {
      periodChangeTimeout = null;
    }
  }, 100); // Small delay to prevent multiple rapid calls
};

// Handle profit period change
let profitPeriodChangeTimeout = null;
const handleProfitPeriodChange = async (period) => {
  // Cancel any pending request
  if (profitPeriodChangeTimeout) {
    clearTimeout(profitPeriodChangeTimeout);
  }

  selectedProfitPeriod.value = period;

  // Use a small timeout to debounce rapid calls
  profitPeriodChangeTimeout = setTimeout(async () => {
    try {
      // Use the new getSiteProfitData method
      const profitData = await dashboardService.getSiteProfitData(period);
      siteProfitData.value = profitData;
    } catch (err) {
      // Only show error if it's not a cancelled request
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching profit data:', err);
        error.value = err.message || `Failed to load profit data for period: ${period}`;
      }
    } finally {
      profitPeriodChangeTimeout = null;
    }
  }, 100); // Small delay to prevent multiple rapid calls
};

onMounted(() => {
  fetchDashboardData();
});

// Clean up resources when component is unmounted
onUnmounted(() => {
  // Cancel any pending timeouts
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
  }
  if (periodChangeTimeout) {
    clearTimeout(periodChangeTimeout);
  }
  if (profitPeriodChangeTimeout) {
    clearTimeout(profitPeriodChangeTimeout);
  }

  // Clear loading timer
  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }
});
</script>

<style scoped>
.admin-dashboard {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.card {
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.notification {
  margin-bottom: 1.5rem;
}

.quick-actions-card {
  margin-bottom: 1.5rem;
  background-color: #1e293b;
  border: 1px solid #374151;
}

.quick-actions-card .card-content {
  padding: 1.25rem;
}

.quick-actions-card .buttons {
  justify-content: space-around;
  flex-wrap: wrap;
}

.quick-actions-card .button {
  margin: 0.5rem;
  min-width: 150px;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.quick-actions-card .button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@media screen and (max-width: 768px) {
  .columns {
    margin-left: 0;
    margin-right: 0;
  }

  .column {
    padding: 0.5rem;
  }

  .buttons {
    justify-content: center;
  }

  .quick-actions-card .button {
    min-width: 120px;
    margin: 0.25rem;
    font-size: 0.9rem;
  }
}
</style>

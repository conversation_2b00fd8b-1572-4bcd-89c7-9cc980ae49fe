using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetProductsByCompanyQueryHandler :
    PaginatedQueryHandler<GetProductsByCompanyQuery, Domain.Entities.Product, ProductResponse>,
    IRequestHandler<GetProductsByCompanyQuery, PaginatedResponse<ProductResponse>>
{
    private readonly IProductRepository _repository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IMapper _mapper;
    private readonly string _baseUrl;

    public GetProductsByCompanyQueryHandler(
        IProductRepository repository,
        ICompanyRepository companyRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
        _companyRepository = companyRepository;
        _mapper = mapper;
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
    }

    public async Task<PaginatedResponse<ProductResponse>> Handle(GetProductsByCompanyQuery request, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(request.CompanyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {request.CompanyId} не знайдено.");


        // Створюємо фільтр для продуктів компанії
        Expression<Func<Domain.Entities.Product, bool>> filter = p => p.CompanyId == company.Id;

        // Додаємо фільтрацію за текстом, якщо вказано
        if (!string.IsNullOrEmpty(request.Filter))
        {
            filter = p => p.CompanyId == company.Id &&
                (p.Name.Contains(request.Filter) || p.Description.Contains(request.Filter));
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy,
            descending: request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 15,
            cancellationToken: cancellationToken,
            includes: p => p.Category
            );

        // Створюємо пагіновану відповідь
        return CreatePaginatedResponse(request, pagedResult, $"companies/{request.CompanyId}/products");
    }
}
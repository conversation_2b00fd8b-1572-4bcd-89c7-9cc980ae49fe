/* Admin Design System - Global Variables and Unified Styles */

:root {
  /* Color Palette */
  --admin-primary: #3273dc;
  --admin-primary-light: #5a8def;
  --admin-primary-dark: #2366d1;
  
  --admin-success: #48c774;
  --admin-success-light: #6bcf7f;
  --admin-success-dark: #3ec46d;
  
  --admin-warning: #ffdd57;
  --admin-warning-light: #ffed70;
  --admin-warning-dark: #ffd83d;
  
  --admin-danger: #f14668;
  --admin-danger-light: #f25a7a;
  --admin-danger-dark: #f03a5f;
  
  --admin-info: #3298dc;
  --admin-info-light: #54a8de;
  --admin-info-dark: #2793da;
  
  --admin-link: #3273dc;
  --admin-link-hover: #2366d1;
  
  /* Text Colors - Enhanced for better readability */
  --admin-text-primary: #1a1a1a;
  --admin-text-secondary: #2d2d2d;
  --admin-text-muted: #5a5a5a;
  --admin-text-light: #8a8a8a;
  --admin-text-white: #ffffff;
  --admin-text-on-dark: #f5f5f5;
  --admin-text-on-dark-secondary: #e0e0e0;
  
  /* Background Colors */
  --admin-bg-primary: #ffffff;
  --admin-bg-secondary: #fafafa;
  --admin-bg-tertiary: #f5f5f5;
  --admin-bg-dark: #363636;
  
  /* Border Colors */
  --admin-border-light: #dbdbdb;
  --admin-border-medium: #b5b5b5;
  --admin-border-dark: #4a4a4a;
  
  /* Typography */
  --admin-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --admin-font-size-xs: 0.75rem;
  --admin-font-size-sm: 0.875rem;
  --admin-font-size-base: 1rem;
  --admin-font-size-lg: 1.125rem;
  --admin-font-size-xl: 1.25rem;
  --admin-font-size-2xl: 1.5rem;
  --admin-font-size-3xl: 1.875rem;
  
  --admin-font-weight-normal: 400;
  --admin-font-weight-medium: 500;
  --admin-font-weight-semibold: 600;
  --admin-font-weight-bold: 700;
  
  --admin-line-height-tight: 1.25;
  --admin-line-height-normal: 1.5;
  --admin-line-height-relaxed: 1.75;
  
  /* Spacing */
  --admin-spacing-xs: 0.25rem;
  --admin-spacing-sm: 0.5rem;
  --admin-spacing-md: 1rem;
  --admin-spacing-lg: 1.5rem;
  --admin-spacing-xl: 2rem;
  --admin-spacing-2xl: 3rem;
  
  /* Border Radius */
  --admin-radius-sm: 0.125rem;
  --admin-radius-md: 0.25rem;
  --admin-radius-lg: 0.5rem;
  --admin-radius-xl: 1rem;
  
  /* Shadows */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --admin-transition-fast: 150ms ease-in-out;
  --admin-transition-normal: 300ms ease-in-out;
  --admin-transition-slow: 500ms ease-in-out;
}

/* Improved Text Readability */
.admin-page-container,
.admin-page-container * {
  font-family: var(--admin-font-family);
  line-height: var(--admin-line-height-normal);
}

/* Enhanced text contrast for dark backgrounds */
.bg-dark,
.bg-dark-blue,
.bg-primary,
.bg-secondary,
.card-dark,
.dark-card,
.admin-dark-bg {
  color: var(--admin-text-on-dark) !important;
}

.bg-dark *,
.bg-dark-blue *,
.bg-primary *,
.bg-secondary *,
.card-dark *,
.dark-card *,
.admin-dark-bg * {
  color: var(--admin-text-on-dark) !important;
}

.bg-dark .text-muted,
.bg-dark-blue .text-muted,
.bg-primary .text-muted,
.bg-secondary .text-muted,
.card-dark .text-muted,
.dark-card .text-muted,
.admin-dark-bg .text-muted {
  color: var(--admin-text-on-dark-secondary) !important;
}

/* Enhanced Page Headers */
.admin-page-header {
  margin-bottom: var(--admin-spacing-xl);
  padding-bottom: var(--admin-spacing-lg);
  border-bottom: 2px solid var(--admin-border-light);
}

.admin-page-title {
  font-size: var(--admin-font-size-3xl);
  font-weight: var(--admin-font-weight-bold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-spacing-sm);
  line-height: var(--admin-line-height-tight);
}

.admin-page-subtitle {
  font-size: var(--admin-font-size-lg);
  color: var(--admin-text-secondary);
  font-weight: var(--admin-font-weight-normal);
  line-height: var(--admin-line-height-normal);
}

/* Unified Card Styles */
.admin-card {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  transition: box-shadow var(--admin-transition-fast);
}

.admin-card:hover {
  box-shadow: var(--admin-shadow-md);
}

.admin-card-header {
  padding: var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-lg) var(--admin-radius-lg) 0 0;
}

.admin-card-title {
  font-size: var(--admin-font-size-xl);
  font-weight: var(--admin-font-weight-semibold);
  color: var(--admin-text-primary);
  margin: 0;
}

.admin-card-content {
  padding: var(--admin-spacing-lg);
}

/* Enhanced Table Styles */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.admin-table th {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-weight-semibold);
  font-size: var(--admin-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--admin-spacing-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-light);
}

.admin-table td {
  padding: var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  color: var(--admin-text-secondary);
  font-size: var(--admin-font-size-base);
  line-height: var(--admin-line-height-normal);
}

.admin-table tr:hover {
  background: var(--admin-bg-tertiary);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

/* Unified Button Styles */
.admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--admin-spacing-sm) var(--admin-spacing-lg);
  font-size: var(--admin-font-size-base);
  font-weight: var(--admin-font-weight-medium);
  line-height: var(--admin-line-height-tight);
  border: 1px solid transparent;
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
  text-decoration: none;
  gap: var(--admin-spacing-sm);
}

.admin-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--admin-shadow-md);
}

.admin-button:active {
  transform: translateY(0);
}

.admin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Button Variants */
.admin-button-primary {
  background: var(--admin-primary);
  color: var(--admin-text-white);
  border-color: var(--admin-primary);
}

.admin-button-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
}

.admin-button-success {
  background: var(--admin-success);
  color: var(--admin-text-white);
  border-color: var(--admin-success);
}

.admin-button-success:hover {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
}

.admin-button-warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
  border-color: var(--admin-warning);
}

.admin-button-warning:hover {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
}

.admin-button-danger {
  background: var(--admin-danger);
  color: var(--admin-text-white);
  border-color: var(--admin-danger);
}

.admin-button-danger:hover {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
}

/* Form Styles */
.admin-form-group {
  margin-bottom: var(--admin-spacing-lg);
}

.admin-form-label {
  display: block;
  font-size: var(--admin-font-size-sm);
  font-weight: var(--admin-font-weight-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Enhanced form labels for dark backgrounds */
.bg-dark .admin-form-label,
.bg-dark-blue .admin-form-label,
.bg-primary .admin-form-label,
.bg-secondary .admin-form-label,
.card-dark .admin-form-label,
.dark-card .admin-form-label,
.admin-dark-bg .admin-form-label,
.bg-dark label,
.bg-dark-blue label,
.bg-primary label,
.bg-secondary label,
.card-dark label,
.dark-card label,
.admin-dark-bg label {
  color: var(--admin-text-on-dark) !important;
  font-weight: var(--admin-font-weight-bold);
}

.admin-form-input,
.admin-form-select,
.admin-form-textarea {
  width: 100%;
  padding: var(--admin-spacing-md);
  font-size: var(--admin-font-size-base);
  line-height: var(--admin-line-height-normal);
  color: var(--admin-text-primary);
  background: var(--admin-bg-primary);
  border: 2px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  transition: border-color var(--admin-transition-fast), box-shadow var(--admin-transition-fast);
}

/* Enhanced form inputs for dark backgrounds */
.bg-dark .admin-form-input,
.bg-dark-blue .admin-form-input,
.bg-primary .admin-form-input,
.bg-secondary .admin-form-input,
.card-dark .admin-form-input,
.dark-card .admin-form-input,
.admin-dark-bg .admin-form-input,
.bg-dark .admin-form-select,
.bg-dark-blue .admin-form-select,
.bg-primary .admin-form-select,
.bg-secondary .admin-form-select,
.card-dark .admin-form-select,
.dark-card .admin-form-select,
.admin-dark-bg .admin-form-select,
.bg-dark .admin-form-textarea,
.bg-dark-blue .admin-form-textarea,
.bg-primary .admin-form-textarea,
.bg-secondary .admin-form-textarea,
.card-dark .admin-form-textarea,
.dark-card .admin-form-textarea,
.admin-dark-bg .admin-form-textarea,
.bg-dark input,
.bg-dark-blue input,
.bg-primary input,
.bg-secondary input,
.card-dark input,
.dark-card input,
.admin-dark-bg input,
.bg-dark select,
.bg-dark-blue select,
.bg-primary select,
.bg-secondary select,
.card-dark select,
.dark-card select,
.admin-dark-bg select,
.bg-dark textarea,
.bg-dark-blue textarea,
.bg-primary textarea,
.bg-secondary textarea,
.card-dark textarea,
.dark-card textarea,
.admin-dark-bg textarea {
  background: rgba(255, 255, 255, 0.95) !important;
  color: var(--admin-text-primary) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.admin-form-input:focus,
.admin-form-select:focus,
.admin-form-textarea:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(50, 115, 220, 0.1);
}

/* Status Tags */
.admin-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
  font-size: var(--admin-font-size-xs);
  font-weight: var(--admin-font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-radius: var(--admin-radius-md);
  gap: var(--admin-spacing-xs);
}

.admin-tag-success {
  background: rgba(72, 199, 116, 0.1);
  color: var(--admin-success-dark);
  border: 1px solid rgba(72, 199, 116, 0.2);
}

.admin-tag-warning {
  background: rgba(255, 221, 87, 0.1);
  color: #b8860b;
  border: 1px solid rgba(255, 221, 87, 0.2);
}

.admin-tag-danger {
  background: rgba(241, 70, 104, 0.1);
  color: var(--admin-danger-dark);
  border: 1px solid rgba(241, 70, 104, 0.2);
}

.admin-tag-info {
  background: rgba(50, 152, 220, 0.1);
  color: var(--admin-info-dark);
  border: 1px solid rgba(50, 152, 220, 0.2);
}

/* Loading States */
.admin-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--admin-spacing-xl);
  color: var(--admin-text-muted);
}

.admin-loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--admin-border-light);
  border-top: 3px solid var(--admin-primary);
  border-radius: 50%;
  animation: admin-spin 1s linear infinite;
  margin-right: var(--admin-spacing-md);
}

@keyframes admin-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced table text readability */
.bg-dark table,
.bg-dark-blue table,
.bg-primary table,
.bg-secondary table,
.card-dark table,
.dark-card table,
.admin-dark-bg table {
  color: var(--admin-text-on-dark) !important;
}

.bg-dark table th,
.bg-dark-blue table th,
.bg-primary table th,
.bg-secondary table th,
.card-dark table th,
.dark-card table th,
.admin-dark-bg table th,
.bg-dark table td,
.bg-dark-blue table td,
.bg-primary table td,
.bg-secondary table td,
.card-dark table td,
.dark-card table td,
.admin-dark-bg table td {
  color: var(--admin-text-on-dark) !important;
}

/* Enhanced text for all dark background elements */
.bg-dark p,
.bg-dark-blue p,
.bg-primary p,
.bg-secondary p,
.card-dark p,
.dark-card p,
.admin-dark-bg p,
.bg-dark span,
.bg-dark-blue span,
.bg-primary span,
.bg-secondary span,
.card-dark span,
.dark-card span,
.admin-dark-bg span,
.bg-dark div,
.bg-dark-blue div,
.bg-primary div,
.bg-secondary div,
.card-dark div,
.dark-card div,
.admin-dark-bg div {
  color: var(--admin-text-on-dark) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-page-title {
    font-size: var(--admin-font-size-2xl);
  }
  
  .admin-page-subtitle {
    font-size: var(--admin-font-size-base);
  }
  
  .admin-card-content,
  .admin-card-header {
    padding: var(--admin-spacing-md);
  }
  
  .admin-table th,
  .admin-table td {
    padding: var(--admin-spacing-md);
    font-size: var(--admin-font-size-sm);
  }
}

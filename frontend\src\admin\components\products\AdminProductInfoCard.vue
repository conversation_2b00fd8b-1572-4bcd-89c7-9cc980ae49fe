<template>
  <AdminCard 
    title="Product Information" 
    :loading="loading"
    shadow="default"
  >
    <template #actions>
      <button 
        v-if="editable"
        class="admin-btn admin-btn-xs admin-btn-secondary"
        @click="$emit('edit')"
      >
        <i class="fas fa-edit"></i>
        Edit
      </button>
    </template>

    <div class="admin-product-info">
      <!-- Basic Information -->
      <div class="admin-info-section">
        <h4 class="admin-info-section-title">Basic Information</h4>
        <div class="admin-info-grid">
          <div class="admin-info-item">
            <span class="admin-info-label">Name</span>
            <span class="admin-info-value">{{ product?.name || 'N/A' }}</span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Slug</span>
            <span class="admin-info-value admin-info-value--code">{{ product?.slug || 'N/A' }}</span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Description</span>
            <div class="admin-info-value admin-info-value--text">
              {{ product?.description || 'No description provided' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Information -->
      <div class="admin-info-section">
        <h4 class="admin-info-section-title">Pricing & Inventory</h4>
        <div class="admin-info-grid">
          <div class="admin-info-item">
            <span class="admin-info-label">Price</span>
            <span class="admin-info-value admin-info-value--price">
              {{ formatPrice(product?.priceAmount, product?.priceCurrency) }}
            </span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Currency</span>
            <span class="admin-info-value">{{ product?.priceCurrency || 'UAH' }}</span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Stock Quantity</span>
            <span class="admin-info-value" :class="getStockClass(product?.stock)">
              {{ product?.stock ?? 'N/A' }}
              <span v-if="product?.stock !== undefined" class="admin-stock-status">
                ({{ getStockStatus(product.stock) }})
              </span>
            </span>
          </div>
        </div>
      </div>

      <!-- Company & Category -->
      <div class="admin-info-section">
        <h4 class="admin-info-section-title">Organization</h4>
        <div class="admin-info-grid">
          <div class="admin-info-item">
            <span class="admin-info-label">Company</span>
            <div class="admin-info-value">
              <router-link
                v-if="product?.companyId"
                :to="`/admin/companies/${product.companyId}`"
                class="admin-info-link"
              >
                <i class="fas fa-building"></i>
                <span v-if="loadingCompany">Loading...</span>
                <span v-else>{{ companyName || 'Unknown Company' }}</span>
              </router-link>
              <span v-else>N/A</span>
            </div>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Category</span>
            <div class="admin-info-value">
              <span v-if="product?.categoryId" class="admin-category-badge">
                <i class="fas fa-tag"></i>
                <span v-if="loadingCategory">Loading...</span>
                <span v-else>{{ categoryName || 'Uncategorized' }}</span>
              </span>
              <span v-else>Uncategorized</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Information -->
      <div class="admin-info-section">
        <h4 class="admin-info-section-title">Status & Dates</h4>
        <div class="admin-info-grid">
          <div class="admin-info-item">
            <span class="admin-info-label">Status</span>
            <span class="admin-status-badge" :class="getStatusClass(product?.status)">
              <i class="fas" :class="getStatusIcon(product?.status)"></i>
              {{ formatStatus(product?.status) }}
            </span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Created</span>
            <span class="admin-info-value admin-info-value--date">
              {{ formatDate(product?.createdAt) }}
            </span>
          </div>
          
          <div class="admin-info-item">
            <span class="admin-info-label">Last Updated</span>
            <span class="admin-info-value admin-info-value--date">
              {{ formatDate(product?.updatedAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </AdminCard>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import AdminCard from '../common/AdminCard.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  }
});

// Reactive data
const companyName = ref('');
const categoryName = ref('');
const loadingCompany = ref(false);
const loadingCategory = ref(false);

// Emits
const emit = defineEmits(['edit']);

// Methods
const formatPrice = (amount, currency = 'UAH') => {
  if (!amount && amount !== 0) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: currency || 'UAH'
  }).format(amount);
};

const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatStatus = (status) => {
  switch (status) {
    case 0: return 'PENDING';
    case 1: return 'APPROVED';
    case 2: return 'REJECTED';
    default: return 'UNKNOWN';
  }
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'admin-status-badge--warning';
    case 1: return 'admin-status-badge--success';
    case 2: return 'admin-status-badge--danger';
    default: return 'admin-status-badge--secondary';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 0: return 'fa-clock';
    case 1: return 'fa-check-circle';
    case 2: return 'fa-times-circle';
    default: return 'fa-question-circle';
  }
};

const getStockClass = (stock) => {
  if (stock === 0) return 'admin-info-value--danger';
  if (stock < 10) return 'admin-info-value--warning';
  return 'admin-info-value--success';
};

// Load company name
const loadCompanyName = async (companyId) => {
  if (!companyId) {
    companyName.value = '';
    return;
  }

  try {
    loadingCompany.value = true;
    console.log('Loading company for ID:', companyId);
    const companies = await productsService.getCompanies({ search: '', pageSize: 1000 });
    console.log('Companies response:', companies);

    // Handle different response structures
    let companiesData = [];
    if (companies.data && Array.isArray(companies.data)) {
      companiesData = companies.data;
    } else if (companies && Array.isArray(companies)) {
      companiesData = companies;
    }

    console.log('Companies data:', companiesData);
    const company = companiesData.find(c => c.id === companyId || c.id === String(companyId));
    console.log('Found company:', company);
    companyName.value = company?.name || 'Unknown Company';
  } catch (error) {
    console.error('Error loading company:', error);
    companyName.value = 'Unknown Company';
  } finally {
    loadingCompany.value = false;
  }
};

// Load category name
const loadCategoryName = async (categoryId) => {
  if (!categoryId) {
    categoryName.value = '';
    return;
  }

  try {
    loadingCategory.value = true;
    console.log('Loading category for ID:', categoryId);
    const categories = await productsService.getCategories({ search: '', pageSize: 1000 });
    console.log('Categories response:', categories);

    // Handle different response structures
    let categoriesData = [];
    if (categories.data && Array.isArray(categories.data)) {
      categoriesData = categories.data;
    } else if (categories && Array.isArray(categories)) {
      categoriesData = categories;
    }

    console.log('Categories data:', categoriesData);
    const category = categoriesData.find(c => c.id === categoryId || c.id === String(categoryId));
    console.log('Found category:', category);
    categoryName.value = category?.name || 'Uncategorized';
  } catch (error) {
    console.error('Error loading category:', error);
    categoryName.value = 'Uncategorized';
  } finally {
    loadingCategory.value = false;
  }
};

// Watch for product changes
watch(() => props.product?.companyId, (newCompanyId) => {
  if (newCompanyId) {
    loadCompanyName(newCompanyId);
  } else {
    companyName.value = '';
  }
}, { immediate: true });

watch(() => props.product?.categoryId, (newCategoryId) => {
  if (newCategoryId) {
    loadCategoryName(newCategoryId);
  } else {
    categoryName.value = '';
  }
}, { immediate: true });

const getStockStatus = (stock) => {
  if (stock === 0) return 'Out of Stock';
  if (stock < 10) return 'Low Stock';
  return 'In Stock';
};
</script>

<style scoped>
.admin-product-info {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-info-section {
  border-bottom: 1px solid var(--admin-border-light);
  padding-bottom: var(--admin-space-lg);
}

.admin-info-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.admin-info-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-md) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-info-section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--admin-primary);
  border-radius: 2px;
}

.admin-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--admin-space-md);
}

.admin-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-info-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-info-value {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-medium);
  word-break: break-word;
}

.admin-info-value--code {
  font-family: var(--admin-font-mono);
  background: var(--admin-bg-tertiary);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
}

.admin-info-value--text {
  line-height: 1.6;
  max-height: 100px;
  overflow-y: auto;
}

.admin-info-value--price {
  color: var(--admin-success);
  font-weight: var(--admin-font-bold);
  font-size: var(--admin-text-lg);
}

.admin-info-value--date {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
}

.admin-info-value--success {
  color: var(--admin-success);
}

.admin-info-value--warning {
  color: var(--admin-warning);
}

.admin-info-value--danger {
  color: var(--admin-danger);
}

.admin-info-link {
  color: var(--admin-link);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  transition: color var(--admin-transition-base);
}

.admin-info-link:hover {
  color: var(--admin-link-hover);
}

.admin-category-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  background: var(--admin-bg-tertiary);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-primary);
}

.admin-stock-status {
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-normal);
  opacity: 0.8;
}

.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-badge--success {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-status-badge--warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-status-badge--danger {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

.admin-status-badge--secondary {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
}

/* Responsive */
@media (min-width: 768px) {
  .admin-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .admin-info-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-info-item {
    padding: var(--admin-space-sm);
    background: var(--admin-bg-secondary);
    border-radius: var(--admin-radius-sm);
  }
}
</style>

<template>
  <AdminCard 
    title="Product Attributes" 
    :loading="loading"
    shadow="default"
  >
    <template #actions>
      <button 
        v-if="editable"
        class="admin-btn admin-btn-xs admin-btn-secondary"
        @click="$emit('edit')"
      >
        <i class="fas fa-edit"></i>
        Edit
      </button>
    </template>

    <div class="admin-attributes-container">
      <!-- Empty State -->
      <div v-if="!parsedAttributes || parsedAttributes.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-list-ul"></i>
        </div>
        <h4 class="admin-empty-title">No Attributes</h4>
        <p class="admin-empty-description">
          This product doesn't have any custom attributes defined.
        </p>
        <button 
          v-if="editable"
          class="admin-btn admin-btn-primary"
          @click="$emit('add-attribute')"
        >
          <i class="fas fa-plus"></i>
          Add Attribute
        </button>
      </div>

      <!-- Attributes Table -->
      <div v-else class="admin-attributes-table">
        <div class="admin-table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th class="admin-table-header">Attribute</th>
                <th class="admin-table-header">Value</th>
                <th class="admin-table-header">Type</th>
                <th v-if="editable" class="admin-table-header admin-table-header--actions">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="(attribute, index) in parsedAttributes" 
                :key="index"
                class="admin-table-row"
              >
                <td class="admin-table-cell">
                  <div class="admin-attribute-key">
                    <i class="fas fa-tag admin-attribute-icon"></i>
                    {{ attribute.key }}
                  </div>
                </td>
                <td class="admin-table-cell">
                  <div class="admin-attribute-value" :class="getValueClass(attribute.value)">
                    {{ formatValue(attribute.value) }}
                  </div>
                </td>
                <td class="admin-table-cell">
                  <span class="admin-type-badge" :class="getTypeBadgeClass(attribute.type)">
                    <i class="fas" :class="getTypeIcon(attribute.type)"></i>
                    {{ attribute.type }}
                  </span>
                </td>
                <td v-if="editable" class="admin-table-cell admin-table-cell--actions">
                  <div class="admin-table-actions">
                    <button 
                      class="admin-btn admin-btn-xs admin-btn-secondary"
                      @click="$emit('edit-attribute', attribute, index)"
                      title="Edit attribute"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button 
                      class="admin-btn admin-btn-xs admin-btn-danger"
                      @click="$emit('delete-attribute', attribute, index)"
                      title="Delete attribute"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Add Attribute Button -->
        <div v-if="editable" class="admin-attributes-footer">
          <button 
            class="admin-btn admin-btn-secondary"
            @click="$emit('add-attribute')"
          >
            <i class="fas fa-plus"></i>
            Add Attribute
          </button>
        </div>
      </div>
    </div>
  </AdminCard>
</template>

<script setup>
import { computed } from 'vue';
import AdminCard from '../common/AdminCard.vue';

// Props
const props = defineProps({
  attributes: {
    type: [String, Object, Array],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['edit', 'add-attribute', 'edit-attribute', 'delete-attribute']);

// Computed
const parsedAttributes = computed(() => {
  if (!props.attributes) return [];
  
  try {
    // If it's already an array
    if (Array.isArray(props.attributes)) {
      return props.attributes.map(attr => ({
        key: attr.key || attr.name || 'Unknown',
        value: attr.value || attr.val || '',
        type: detectType(attr.value || attr.val)
      }));
    }
    
    // If it's a JSON string
    if (typeof props.attributes === 'string') {
      const parsed = JSON.parse(props.attributes);
      if (Array.isArray(parsed)) {
        return parsed.map(attr => ({
          key: attr.key || attr.name || 'Unknown',
          value: attr.value || attr.val || '',
          type: detectType(attr.value || attr.val)
        }));
      } else {
        // Convert object to array
        return Object.entries(parsed).map(([key, value]) => ({
          key,
          value,
          type: detectType(value)
        }));
      }
    }
    
    // If it's an object
    if (typeof props.attributes === 'object') {
      return Object.entries(props.attributes).map(([key, value]) => ({
        key,
        value,
        type: detectType(value)
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error parsing attributes:', error);
    return [];
  }
});

// Methods
const detectType = (value) => {
  if (value === null || value === undefined) return 'null';
  if (typeof value === 'boolean') return 'boolean';
  if (typeof value === 'number') return 'number';
  if (typeof value === 'string') {
    // Check if it's a URL
    if (value.startsWith('http://') || value.startsWith('https://')) return 'url';
    // Check if it's an email
    if (value.includes('@') && value.includes('.')) return 'email';
    // Check if it's a date
    if (!isNaN(Date.parse(value)) && value.includes('-')) return 'date';
    return 'string';
  }
  if (Array.isArray(value)) return 'array';
  if (typeof value === 'object') return 'object';
  return 'unknown';
};

const formatValue = (value) => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  if (Array.isArray(value)) return value.join(', ');
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const getValueClass = (value) => {
  const type = detectType(value);
  return {
    'admin-attribute-value--boolean': type === 'boolean',
    'admin-attribute-value--number': type === 'number',
    'admin-attribute-value--url': type === 'url',
    'admin-attribute-value--email': type === 'email',
    'admin-attribute-value--date': type === 'date',
    'admin-attribute-value--null': type === 'null'
  };
};

const getTypeBadgeClass = (type) => {
  return {
    'admin-type-badge--string': type === 'string',
    'admin-type-badge--number': type === 'number',
    'admin-type-badge--boolean': type === 'boolean',
    'admin-type-badge--url': type === 'url',
    'admin-type-badge--email': type === 'email',
    'admin-type-badge--date': type === 'date',
    'admin-type-badge--array': type === 'array',
    'admin-type-badge--object': type === 'object',
    'admin-type-badge--null': type === 'null'
  };
};

const getTypeIcon = (type) => {
  switch (type) {
    case 'string': return 'fa-font';
    case 'number': return 'fa-hashtag';
    case 'boolean': return 'fa-toggle-on';
    case 'url': return 'fa-link';
    case 'email': return 'fa-envelope';
    case 'date': return 'fa-calendar';
    case 'array': return 'fa-list';
    case 'object': return 'fa-code';
    case 'null': return 'fa-ban';
    default: return 'fa-question';
  }
};
</script>

<style scoped>
.admin-attributes-container {
  min-height: 200px;
}

/* Empty State */
.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xl);
  text-align: center;
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-text-muted);
  margin-bottom: var(--admin-space-md);
}

.admin-empty-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-empty-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
  margin: 0 0 var(--admin-space-lg) 0;
  max-width: 300px;
}

/* Table */
.admin-attributes-table {
  width: 100%;
}

.admin-table-container {
  overflow-x: auto;
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-bg-primary);
}

.admin-table-header {
  background: var(--admin-bg-secondary);
  padding: var(--admin-space-md);
  text-align: left;
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  border-bottom: 1px solid var(--admin-border-light);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-table-header--actions {
  width: 120px;
  text-align: center;
}

.admin-table-row:hover {
  background: var(--admin-bg-secondary);
}

.admin-table-cell {
  padding: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: top;
}

.admin-table-cell--actions {
  text-align: center;
}

/* Attribute Key */
.admin-attribute-key {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-attribute-icon {
  color: var(--admin-primary);
  font-size: var(--admin-text-sm);
}

/* Attribute Value */
.admin-attribute-value {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  word-break: break-word;
  max-width: 300px;
}

.admin-attribute-value--boolean {
  color: var(--admin-info);
  font-weight: var(--admin-font-semibold);
}

.admin-attribute-value--number {
  color: var(--admin-success);
  font-weight: var(--admin-font-semibold);
}

.admin-attribute-value--url {
  color: var(--admin-link);
}

.admin-attribute-value--email {
  color: var(--admin-link);
}

.admin-attribute-value--date {
  color: var(--admin-warning);
}

.admin-attribute-value--null {
  color: var(--admin-text-muted);
  font-style: italic;
}

/* Type Badge */
.admin-type-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-type-badge--string {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
}

.admin-type-badge--number {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-type-badge--boolean {
  background: var(--admin-info);
  color: var(--admin-text-white);
}

.admin-type-badge--url,
.admin-type-badge--email {
  background: var(--admin-link);
  color: var(--admin-text-white);
}

.admin-type-badge--date {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-type-badge--array,
.admin-type-badge--object {
  background: var(--admin-primary);
  color: var(--admin-text-white);
}

.admin-type-badge--null {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

/* Actions */
.admin-table-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-xs);
}

/* Footer */
.admin-attributes-footer {
  padding: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-table-container {
    font-size: var(--admin-text-sm);
  }
  
  .admin-table-header,
  .admin-table-cell {
    padding: var(--admin-space-sm);
  }
  
  .admin-attribute-value {
    max-width: 200px;
  }
  
  .admin-table-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
}
</style>

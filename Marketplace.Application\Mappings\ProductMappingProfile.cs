﻿using AutoMapper;
using Marketplace.Application.Commands.Product;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Mappings
{
    public class ProductMappingProfile : Profile
    {
        public ProductMappingProfile()
        {
            // Мапінг для створення продукту (StoreProductCommand -> Product)
            CreateMap<StoreProductCommand, Product>()
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Slug, opt => opt.MapFrom(src => src.Slug))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => new Money(src.PriceAmount, src.PriceCurrency)))
                .ForMember(dest => dest.Stock, opt => opt.MapFrom(src => src.Stock))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategoryId))
                .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes))
                .ForMember(dest => dest.Meta, opt => opt.MapFrom(src => new Meta(src.MetaTitle, src.MetaDescription, src.MetaImage != null ? new Url(src.MetaImage) : null)))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid())) // генеруємо новий Id
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow)) // генеруємо час створення
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => (DateTime?)null));


            CreateMap<Product, ProductResponse>()
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Slug, opt => opt.MapFrom(src => src.Slug.Value))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.PriceAmount, opt => opt.MapFrom(src => src.Price.Amount))
                .ForMember(dest => dest.PriceCurrency, opt => opt.MapFrom(src => src.Price.Currency))
                .ForMember(dest => dest.Stock, opt => opt.MapFrom(src => src.Stock))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategoryId))
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category != null ? src.Category.Name : "Unknown"))
                .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.MetaTitle, opt => opt.MapFrom(src => src.Meta.Title))
                .ForMember(dest => dest.MetaImage, opt => opt.MapFrom(src => src.Meta.Image != null ? src.Meta.Image.Value : null))
                .ForMember(dest => dest.MetaDescription, opt => opt.MapFrom(src => src.Meta.Description))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt));

            // Мапінг для оновлення продукту (UpdateProductCommand -> Product)
            //CreateMap<UpdateProductCommand, Product>()
            //    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            //    .ForMember(dest => dest.Slug, opt => opt.MapFrom(src => src.Slug))
            //    .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            //    .ForMember(dest => dest.Price, opt => opt.MapFrom(src => new Money(src.PriceAmount, src.PriceCurrency)))
            //    .ForMember(dest => dest.Stock, opt => opt.MapFrom(src => src.Stock))
            //    .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.CategoryId))
            //    .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes))
            //    .ForMember(dest => dest.Meta, opt => opt.MapFrom(src => new Meta(src.MetaTitle, src.MetaDescription, src.MetaImage)))
            //    .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.Now)); // оновлюємо час останнього оновлення
        }

        private static string GetCategoryHierarchy(Category? category)
        {
            if (category == null)
                return "Unknown";

            var hierarchy = new List<string>();
            var current = category;

            // Збираємо ієрархію від поточної категорії до кореневої
            while (current != null)
            {
                hierarchy.Insert(0, current.Name); // Вставляємо на початок для правильного порядку
                current = current.Parent; // Переходимо до батьківської категорії
            }

            return string.Join(" > ", hierarchy);
        }
    }
}

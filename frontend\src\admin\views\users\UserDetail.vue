<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Users
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-user admin-page-icon"></i>
          User Details
        </h1>
        <p class="admin-page-subtitle">View and manage user information</p>
      </div>
      <div class="admin-page-actions">
        <router-link
          :to="`/admin/users/${userId}/edit`"
          class="admin-btn admin-btn-primary">
          <i class="fas fa-edit"></i>
          Edit User
        </router-link>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading user data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading user data</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadUser" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <!-- User Details Content -->
    <div v-else-if="user" class="admin-user-detail-content">
      <!-- Basic Information Card -->
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-user-circle"></i>
            User Information
          </h3>
        </div>
        <div class="admin-card-content">
          <div class="admin-user-detail-grid">
            <div class="admin-detail-section">
              <h4 class="admin-detail-section-title">Basic Information</h4>
              
              <div class="admin-detail-row">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Username</label>
                  <div class="admin-detail-value">{{ user.username }}</div>
                </div>
                
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Email</label>
                  <div class="admin-detail-value">{{ user.email }}</div>
                </div>
              </div>

              <div class="admin-detail-row">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Role</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge" :class="getRoleClass(user.role)">
                      {{ getRoleDisplayName(user.role) }}
                    </span>
                  </div>
                </div>

                <div class="admin-detail-item">
                  <label class="admin-detail-label">Status</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge admin-badge-success">Active</span>
                  </div>
                </div>
              </div>

              <div class="admin-detail-row" v-if="user.birthday">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Birthday</label>
                  <div class="admin-detail-value">{{ formatDate(user.birthday) }}</div>
                </div>
                
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Registered</label>
                  <div class="admin-detail-value">{{ formatDate(user.createdAt || user.emailConfirmedAt) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order History Card -->
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-shopping-cart"></i>
            Order History
          </h3>
          <div class="admin-card-actions">
            <span class="admin-results-count">{{ totalOrders }} orders found</span>
          </div>
        </div>
        <div class="admin-card-content">
          <!-- Orders will be implemented later -->
          <div class="admin-empty-state">
            <div class="admin-empty-icon">
              <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="admin-empty-title">No Orders Found</h3>
            <p class="admin-empty-message">
              This user hasn't placed any orders yet.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';
import { ROLE_KEYS, ROLE_DISPLAY_NAMES, getRoleKey } from '@/admin/services/roles';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const error = ref(null);
const user = ref(null);
const totalOrders = ref(0);

// Computed
const userId = computed(() => route.params.id);

// Methods
const loadUser = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const userData = await usersService.getUserById(userId.value);
    user.value = userData;
    
    // Load orders count (placeholder for now)
    totalOrders.value = 0;
  } catch (err) {
    console.error('Error loading user:', err);
    error.value = 'Failed to load user data. Please try again.';
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push('/admin/users');
};

const getRoleClass = (role) => {
  // Convert role from backend format (number) to role key
  const roleKey = getRoleKey(role);

  const roleClasses = {
    [ROLE_KEYS.ADMIN]: 'admin-badge-danger',
    [ROLE_KEYS.MODERATOR]: 'admin-badge-warning',
    [ROLE_KEYS.SELLER_OWNER]: 'admin-badge-info',
    [ROLE_KEYS.SELLER]: 'admin-badge-primary',
    [ROLE_KEYS.BUYER]: 'admin-badge-secondary'
  };
  return roleClasses[roleKey] || 'admin-badge-secondary';
};

const getRoleDisplayName = (role) => {
  // Convert role from backend format (number) to display name
  const roleKey = getRoleKey(role);
  return ROLE_DISPLAY_NAMES[roleKey] || role;
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Lifecycle
onMounted(() => {
  loadUser();
});
</script>

<style scoped>
.admin-user-detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.admin-user-detail-grid {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.admin-detail-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-detail-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  padding-bottom: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border);
}

.admin-detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-xl);
}

.admin-detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-detail-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-detail-value {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-medium);
}

@media (max-width: 768px) {
  .admin-detail-row {
    grid-template-columns: 1fr;
    gap: var(--admin-space-lg);
  }
}
</style>

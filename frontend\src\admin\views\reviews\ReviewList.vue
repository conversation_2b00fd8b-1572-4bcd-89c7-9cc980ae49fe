<template>
  <div class="review-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Reviews Management</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              class="button is-danger"
              @click="bulkDelete"
              :disabled="selectedReviews.length === 0 || actionLoading"
              v-if="selectedReviews.length > 0"
            >
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
              <span>Delete Selected ({{ selectedReviews.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Reviews"
      search-placeholder="Search by comment, product name, or user name..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="reviews"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading reviews...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchData">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Reviews Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th>Product</th>
                <th>User</th>
                <th>Rating</th>
                <th>Comment</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="review in reviews" :key="review.id">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="review.id"
                      v-model="selectedReviews"
                    />
                  </label>
                </td>
                <td>
                  <strong>{{ review.productName || 'Unknown Product' }}</strong>
                </td>
                <td>{{ review.userName || 'Unknown User' }}</td>
                <td>
                  <div class="stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= getAverageRating(review) }">
                      ★
                    </span>
                  </div>
                </td>
                <td>
                  <span class="has-text-grey">
                    {{ truncateText(review.comment, 50) }}
                  </span>
                </td>
                <td>{{ formatDate(review.createdAt) }}</td>
                <td>
                  <div class="buttons">
                    <router-link
                      :to="{ name: 'AdminReviewDetail', params: { id: review.id } }"
                      class="button is-small is-info">
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                      <span>View</span>
                    </router-link>
                    <button
                      class="button is-small is-danger"
                      @click="deleteReview(review.id)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                      <span>Delete</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { reviewsService } from '@/admin/services/reviews';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/components/admin/Pagination.vue';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Filter configuration
const filterFields = [
  {
    key: 'rating',
    label: 'Rating',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: '', label: 'All Ratings' },
      { value: '1', label: '1 Star' },
      { value: '2', label: '2 Stars' },
      { value: '3', label: '3 Stars' },
      { value: '4', label: '4 Stars' },
      { value: '5', label: '5 Stars' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'CreatedAt', label: 'Date Created' },
      { value: 'UpdatedAt', label: 'Date Updated' },
      { value: 'Comment', label: 'Comment' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items: reviews,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: reviewsService.getReviews,
  defaultFilters: {
    rating: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Additional reactive data
const actionLoading = ref(false);
const selectedReviews = ref([]);

// Computed
const allSelected = computed(() => {
  return reviews.value.length > 0 && selectedReviews.value.length === reviews.value.length;
});

const someSelected = computed(() => {
  return selectedReviews.value.length > 0 && selectedReviews.value.length < reviews.value.length;
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = filterFields.find(f => f.key === key)?.options?.[0]?.value || '';
    }
  });
  fetchData(1);
};

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedReviews.value = [];
  } else {
    selectedReviews.value = reviews.value.map(review => review.id);
  }
};

const deleteReview = async (id) => {
  if (!confirm('Are you sure you want to delete this review?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await reviewsService.deleteReview(id);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete review';
  } finally {
    actionLoading.value = false;
  }
};

const bulkDelete = async () => {
  if (!confirm(`Are you sure you want to delete ${selectedReviews.value.length} selected reviews?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    await reviewsService.bulkDeleteReviews(selectedReviews.value);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete reviews';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};

const truncateText = (text, length) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

const getAverageRating = (review) => {
  if (!review.rating) return 0;
  // Calculate average from service, deliveryTime, and accuracy
  const { service = 0, deliveryTime = 0, accuracy = 0 } = review.rating;
  return Math.round((service + deliveryTime + accuracy) / 3);
};
</script>

<style scoped>
.review-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--darker-bg);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1.2em;
}

.star.is-filled {
  color: #ffd700;
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.button.is-danger:hover {
  background-color: var(--danger-color-dark);
}

.button.is-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.button.is-info:hover {
  background-color: var(--info-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}
</style>

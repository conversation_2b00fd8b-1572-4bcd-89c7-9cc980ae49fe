<template>
  <AdminCard 
    title="SEO & Metadata" 
    :loading="loading"
    shadow="default"
  >
    <template #actions>
      <button 
        v-if="editable"
        class="admin-btn admin-btn-xs admin-btn-secondary"
        @click="$emit('edit')"
      >
        <i class="fas fa-edit"></i>
        Edit
      </button>
    </template>

    <div class="admin-metadata-container">
      <!-- SEO Information -->
      <div class="admin-metadata-section">
        <h4 class="admin-metadata-section-title">
          <i class="fas fa-search"></i>
          SEO Information
        </h4>
        <div class="admin-metadata-grid">
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Meta Title</span>
            <div class="admin-metadata-value">
              {{ product?.metaTitle || 'No meta title set' }}
            </div>
            <div v-if="product?.metaTitle" class="admin-metadata-stats">
              <span class="admin-stat-item" :class="getTitleLengthClass(product.metaTitle)">
                <i class="fas fa-ruler"></i>
                {{ product.metaTitle.length }}/60 characters
              </span>
            </div>
          </div>
          
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Meta Description</span>
            <div class="admin-metadata-value admin-metadata-value--text">
              {{ product?.metaDescription || 'No meta description set' }}
            </div>
            <div v-if="product?.metaDescription" class="admin-metadata-stats">
              <span class="admin-stat-item" :class="getDescriptionLengthClass(product.metaDescription)">
                <i class="fas fa-ruler"></i>
                {{ product.metaDescription.length }}/160 characters
              </span>
            </div>
          </div>

          <div class="admin-metadata-item admin-metadata-item--full">
            <span class="admin-metadata-label">Meta Image</span>
            <div class="admin-metadata-value">
              <div v-if="product?.metaImage" class="admin-meta-image-preview">
                <img
                  :src="product.metaImage"
                  :alt="product.name || 'Meta image'"
                  class="admin-meta-image"
                  @error="handleImageError"
                />
                <div class="admin-meta-image-info">
                  <div class="admin-meta-image-url">
                    <i class="fas fa-link"></i>
                    <a :href="product.metaImage" target="_blank" rel="noopener">
                      {{ product.metaImage }}
                    </a>
                  </div>
                  <div class="admin-meta-image-usage">
                    <i class="fas fa-info-circle"></i>
                    Used for social media sharing and search engine previews
                  </div>
                </div>
              </div>
              <div v-else class="admin-meta-image-placeholder">
                <i class="fas fa-image"></i>
                <span>No meta image set</span>
                <small>Add a meta image to improve social media sharing</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Technical Metadata -->
      <div class="admin-metadata-section">
        <h4 class="admin-metadata-section-title">
          <i class="fas fa-cog"></i>
          Technical Information
        </h4>
        <div class="admin-metadata-grid">
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Product ID</span>
            <div class="admin-metadata-value admin-metadata-value--code">
              {{ product?.id || 'N/A' }}
            </div>
            <button 
              v-if="product?.id"
              class="admin-copy-btn"
              @click="copyToClipboard(product.id)"
              title="Copy ID"
            >
              <i class="fas fa-copy"></i>
            </button>
          </div>
          
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Slug</span>
            <div class="admin-metadata-value admin-metadata-value--code">
              {{ product?.slug || 'No slug set' }}
            </div>
            <button 
              v-if="product?.slug"
              class="admin-copy-btn"
              @click="copyToClipboard(product.slug)"
              title="Copy slug"
            >
              <i class="fas fa-copy"></i>
            </button>
          </div>
          
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Created Date</span>
            <div class="admin-metadata-value admin-metadata-value--date">
              {{ formatDate(product?.createdAt) }}
            </div>
          </div>
          
          <div class="admin-metadata-item">
            <span class="admin-metadata-label">Last Modified</span>
            <div class="admin-metadata-value admin-metadata-value--date">
              {{ formatDate(product?.updatedAt) }}
            </div>
          </div>
        </div>
      </div>

      <!-- URL Preview -->
      <div v-if="product?.slug" class="admin-metadata-section">
        <h4 class="admin-metadata-section-title">
          <i class="fas fa-link"></i>
          URL Preview
        </h4>
        <div class="admin-url-preview">
          <div class="admin-url-preview-item">
            <span class="admin-url-label">Product URL:</span>
            <div class="admin-url-value">
              <a 
                :href="getProductUrl(product.slug)" 
                target="_blank" 
                class="admin-url-link"
                rel="noopener noreferrer"
              >
                {{ getProductUrl(product.slug) }}
                <i class="fas fa-external-link-alt"></i>
              </a>
              <button 
                class="admin-copy-btn"
                @click="copyToClipboard(getProductUrl(product.slug))"
                title="Copy URL"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- SEO Preview -->
      <div v-if="product?.metaTitle || product?.metaDescription" class="admin-metadata-section">
        <h4 class="admin-metadata-section-title">
          <i class="fab fa-google"></i>
          Search Engine Preview
        </h4>
        <div class="admin-seo-preview">
          <div class="admin-seo-preview-title">
            {{ product.metaTitle || product.name || 'Product Title' }}
          </div>
          <div class="admin-seo-preview-url">
            {{ getProductUrl(product.slug || 'product-slug') }}
          </div>
          <div class="admin-seo-preview-description">
            {{ product.metaDescription || product.description || 'Product description will appear here...' }}
          </div>
        </div>
      </div>
    </div>
  </AdminCard>
</template>

<script setup>
import AdminCard from '../common/AdminCard.vue';

// Props
const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['edit']);

// Methods
const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getTitleLengthClass = (title) => {
  const length = title?.length || 0;
  if (length <= 50) return 'admin-stat-item--success';
  if (length <= 60) return 'admin-stat-item--warning';
  return 'admin-stat-item--danger';
};

const getDescriptionLengthClass = (description) => {
  const length = description?.length || 0;
  if (length <= 140) return 'admin-stat-item--success';
  if (length <= 160) return 'admin-stat-item--warning';
  return 'admin-stat-item--danger';
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
  event.target.nextElementSibling?.classList.add('admin-meta-image-error');
};

const getProductUrl = (slug) => {
  // This would typically come from your app configuration
  const baseUrl = window.location.origin;
  return `${baseUrl}/products/${slug}`;
};

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    // You could show a toast notification here
    console.log('Copied to clipboard:', text);
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
};
</script>

<style scoped>
.admin-metadata-container {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

/* Section */
.admin-metadata-section {
  border-bottom: 1px solid var(--admin-border-light);
  padding-bottom: var(--admin-space-lg);
}

.admin-metadata-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.admin-metadata-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-md) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-metadata-section-title i {
  color: var(--admin-primary);
}

/* Grid */
.admin-metadata-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--admin-space-md);
}

.admin-metadata-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
  position: relative;
}

.admin-metadata-item--full {
  grid-column: 1 / -1;
}

.admin-metadata-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-metadata-value {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-medium);
  word-break: break-word;
  min-height: 1.5em;
}

.admin-metadata-value--code {
  font-family: var(--admin-font-mono);
  background: var(--admin-bg-tertiary);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
  display: inline-block;
}

.admin-metadata-value--text {
  line-height: 1.6;
  max-height: 100px;
  overflow-y: auto;
}

.admin-metadata-value--date {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

/* Stats */
.admin-metadata-stats {
  display: flex;
  gap: var(--admin-space-sm);
  margin-top: var(--admin-space-xs);
}

.admin-stat-item {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
}

.admin-stat-item--success {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-stat-item--warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-stat-item--danger {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

/* Copy Button */
.admin-copy-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  font-size: var(--admin-text-sm);
}

.admin-copy-btn:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-primary);
}

/* URL Preview */
.admin-url-preview {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
}

.admin-url-preview-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-url-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
}

.admin-url-value {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-url-link {
  color: var(--admin-link);
  text-decoration: none;
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  transition: color var(--admin-transition-base);
  word-break: break-all;
}

.admin-url-link:hover {
  color: var(--admin-link-hover);
}

/* SEO Preview */
.admin-seo-preview {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-family: Arial, sans-serif;
}

.admin-seo-preview-title {
  color: #1a0dab;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.3;
  margin-bottom: 2px;
  cursor: pointer;
}

.admin-seo-preview-title:hover {
  text-decoration: underline;
}

.admin-seo-preview-url {
  color: #006621;
  font-size: 14px;
  line-height: 1.3;
  margin-bottom: 4px;
  word-break: break-all;
}

.admin-seo-preview-description {
  color: #545454;
  font-size: 14px;
  line-height: 1.4;
  max-height: 3.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Responsive */
@media (min-width: 768px) {
  .admin-metadata-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-metadata-item {
    padding: var(--admin-space-sm);
    background: var(--admin-bg-secondary);
    border-radius: var(--admin-radius-sm);
  }
  
  .admin-copy-btn {
    position: static;
    align-self: flex-start;
    margin-top: var(--admin-space-xs);
  }
  
  .admin-url-value {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Meta Image Styles */
.admin-meta-image-preview {
  display: flex;
  gap: var(--admin-space-md);
  align-items: flex-start;
}

.admin-meta-image {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-border-light);
  flex-shrink: 0;
}

.admin-meta-image-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-meta-image-url {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
}

.admin-meta-image-url a {
  color: var(--admin-primary);
  text-decoration: none;
  word-break: break-all;
}

.admin-meta-image-url a:hover {
  text-decoration: underline;
}

.admin-meta-image-usage {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

.admin-meta-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xl);
  border: 2px dashed var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  text-align: center;
  color: var(--admin-text-muted);
}

.admin-meta-image-placeholder i {
  font-size: var(--admin-text-2xl);
  margin-bottom: var(--admin-space-sm);
}

.admin-meta-image-placeholder span {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-xs);
}

.admin-meta-image-placeholder small {
  font-size: var(--admin-text-sm);
}
</style>

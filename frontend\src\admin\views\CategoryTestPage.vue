<template>
  <div class="container">
    <div class="columns">
      <div class="column">
        <div class="box">
          <h1 class="title">Category Management Test Page</h1>
          <p class="subtitle">Test category deletion validation and bulk product category updates with mock data</p>
          
          <!-- Error/Success Messages -->
          <div v-if="error" class="notification is-danger">
            <button class="delete" @click="error = ''"></button>
            {{ error }}
          </div>
          
          <div v-if="success" class="notification is-success">
            <button class="delete" @click="success = ''"></button>
            {{ success }}
          </div>

          <!-- Categories Table -->
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Products Count</th>
                  <th>Subcategories Count</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="category in mockCategories" :key="category.id">
                  <td>{{ category.name }}</td>
                  <td>{{ category.productsCount }}</td>
                  <td>{{ category.subcategoriesCount }}</td>
                  <td>
                    <div class="buttons">
                      <button 
                        class="button is-small is-info"
                        @click="viewCategory(category)">
                        View
                      </button>
                      <button 
                        class="button is-small is-warning"
                        @click="showBulkUpdateModal(category)"
                        :disabled="category.productsCount === 0">
                        Move Products
                      </button>
                      <button 
                        class="button is-small is-danger"
                        @click="deleteCategory(category)">
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Category Details Modal -->
    <div class="modal" :class="{ 'is-active': showDetailsModal }">
      <div class="modal-background" @click="closeDetailsModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Category Details: {{ selectedCategory?.name }}</p>
          <button class="delete" @click="closeDetailsModal"></button>
        </header>
        <section class="modal-card-body">
          <div v-if="selectedCategory">
            <p><strong>Products ({{ selectedCategory.productsCount }}):</strong></p>
            <ul v-if="selectedCategory.products.length > 0">
              <li v-for="product in selectedCategory.products" :key="product.id">
                {{ product.name }} - ${{ product.price }}
              </li>
            </ul>
            <p v-else class="has-text-grey">No products in this category</p>
            
            <p class="mt-4"><strong>Subcategories ({{ selectedCategory.subcategoriesCount }}):</strong></p>
            <ul v-if="selectedCategory.subcategories.length > 0">
              <li v-for="subcategory in selectedCategory.subcategories" :key="subcategory.id">
                {{ subcategory.name }}
              </li>
            </ul>
            <p v-else class="has-text-grey">No subcategories</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="closeDetailsModal">Close</button>
        </footer>
      </div>
    </div>

    <!-- Bulk Update Modal -->
    <div class="modal" :class="{ 'is-active': showBulkModal }">
      <div class="modal-background" @click="closeBulkModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Move All Products from "{{ selectedCategory?.name }}"</p>
          <button class="delete" @click="closeBulkModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Select Target Category</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="targetCategoryId">
                  <option value="">Choose a category...</option>
                  <option 
                    v-for="cat in availableTargetCategories" 
                    :key="cat.id" 
                    :value="cat.id">
                    {{ cat.name }}
                  </option>
                </select>
              </div>
            </div>
            <p class="help">All {{ selectedCategory?.productsCount || 0 }} products will be moved to the selected category.</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-warning" 
            @click="bulkUpdateProducts"
            :disabled="!targetCategoryId || bulkUpdateLoading"
            :class="{ 'is-loading': bulkUpdateLoading }">
            Move Products
          </button>
          <button class="button" @click="closeBulkModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Mock data
const mockCategories = ref([
  {
    id: '1',
    name: 'Electronics',
    productsCount: 5,
    subcategoriesCount: 2,
    products: [
      { id: '1', name: 'Laptop', price: 999 },
      { id: '2', name: 'Phone', price: 599 },
      { id: '3', name: 'Tablet', price: 399 },
      { id: '4', name: 'Headphones', price: 199 },
      { id: '5', name: 'Smartwatch', price: 299 }
    ],
    subcategories: [
      { id: '11', name: 'Computers' },
      { id: '12', name: 'Mobile Devices' }
    ]
  },
  {
    id: '2',
    name: 'Clothing',
    productsCount: 3,
    subcategoriesCount: 0,
    products: [
      { id: '6', name: 'T-Shirt', price: 25 },
      { id: '7', name: 'Jeans', price: 75 },
      { id: '8', name: 'Sneakers', price: 120 }
    ],
    subcategories: []
  },
  {
    id: '3',
    name: 'Books',
    productsCount: 0,
    subcategoriesCount: 1,
    products: [],
    subcategories: [
      { id: '31', name: 'Fiction' }
    ]
  },
  {
    id: '4',
    name: 'Empty Category',
    productsCount: 0,
    subcategoriesCount: 0,
    products: [],
    subcategories: []
  }
]);

// State
const error = ref('');
const success = ref('');
const showDetailsModal = ref(false);
const showBulkModal = ref(false);
const selectedCategory = ref(null);
const targetCategoryId = ref('');
const bulkUpdateLoading = ref(false);

// Computed
const availableTargetCategories = computed(() => {
  return mockCategories.value.filter(cat => cat.id !== selectedCategory.value?.id);
});

// Methods
const viewCategory = (category) => {
  selectedCategory.value = category;
  showDetailsModal.value = true;
};

const closeDetailsModal = () => {
  showDetailsModal.value = false;
  selectedCategory.value = null;
};

const showBulkUpdateModal = (category) => {
  selectedCategory.value = category;
  targetCategoryId.value = '';
  showBulkModal.value = true;
};

const closeBulkModal = () => {
  showBulkModal.value = false;
  selectedCategory.value = null;
  targetCategoryId.value = '';
};

const deleteCategory = (category) => {
  error.value = '';
  success.value = '';
  
  // Simulate validation
  if (category.subcategoriesCount > 0) {
    error.value = `Неможливо видалити категорію "${category.name}", оскільки вона містить ${category.subcategoriesCount} підкатегорій. Спочатку видаліть або перемістіть всі підкатегорії.`;
    return;
  }
  
  if (category.productsCount > 0) {
    error.value = `Неможливо видалити категорію "${category.name}", оскільки вона містить ${category.productsCount} продуктів. Спочатку перемістіть або видаліть всі продукти з цієї категорії.`;
    return;
  }
  
  // Simulate successful deletion
  const index = mockCategories.value.findIndex(c => c.id === category.id);
  if (index !== -1) {
    mockCategories.value.splice(index, 1);
    success.value = `Категорію "${category.name}" успішно видалено.`;
  }
};

const bulkUpdateProducts = () => {
  if (!targetCategoryId.value || !selectedCategory.value) return;
  
  bulkUpdateLoading.value = true;
  
  // Simulate API call delay
  setTimeout(() => {
    const targetCategory = mockCategories.value.find(c => c.id === targetCategoryId.value);
    const sourceCategory = selectedCategory.value;
    
    if (targetCategory && sourceCategory) {
      // Move products
      targetCategory.products.push(...sourceCategory.products);
      targetCategory.productsCount += sourceCategory.productsCount;
      
      sourceCategory.products = [];
      sourceCategory.productsCount = 0;
      
      success.value = `Успішно переміщено ${sourceCategory.productsCount || 'всі'} продуктів з "${sourceCategory.name}" до "${targetCategory.name}".`;
    }
    
    bulkUpdateLoading.value = false;
    closeBulkModal();
  }, 1000);
};
</script>

<style scoped>
.container {
  padding: 2rem;
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.table td {
  vertical-align: middle;
}
</style>

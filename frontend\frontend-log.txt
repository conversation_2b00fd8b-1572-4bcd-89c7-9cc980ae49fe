
> frontend@1.0.0 dev
> vite

Port 3000 is in use, trying another one...
Port 3001 is in use, trying another one...
Port 3002 is in use, trying another one...

  [32m[1mVITE[22m v6.3.3[39m  [2mready in [0m[1m537[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3003[22m/[39m
[2m  [32m➜[39m  [1mNetwork[22m[2m: use [22m[1m--host[22m[2m to expose[22m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
[1m[33m[@vue/compiler-sfc][0m[33m `defineProps` is a compiler macro and no longer needs to be imported.[0m

[2m18:20:17[22m [31m[1m[vite][22m[39m [31m[2m(client)[22m[39m Pre-transform error: Failed to parse source for import analysis because the content contains invalid JS syntax. If you are using JSX, make sure to name the file with the .jsx or .tsx extension.
  Plugin: [35mvite:import-analysis[39m
  File: [36mD:/Diplomkla_VS_Code/Nash Git/Marketplace/frontend/src/store/modules/auth.js[39m:1:1
[33m  1  |  import AuthService from '../../services/auth.service';
     |  ^
  2  |  
  3  |  const user = JSON.parse(localStorage.getItem('user'));[39m
[2m18:20:17[22m [31m[1m[vite][22m[39m [31m[2m(client)[22m[39m Pre-transform error: Failed to parse source for import analysis because the content contains invalid JS syntax. If you are using JSX, make sure to name the file with the .jsx or .tsx extension.
  Plugin: [35mvite:import-analysis[39m
  File: [36mD:/Diplomkla_VS_Code/Nash Git/Marketplace/frontend/src/admin/services/categories.js[39m:1:1
[33m  1  |  <<<<<<< HEAD
     |  ^
  2  |  import apiService from '@/services/api';
  3  |  [39m
[2m18:20:17[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to parse source for import analysis because the content contains invalid JS syntax. If you are using JSX, make sure to name the file with the .jsx or .tsx extension.[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mD:/Diplomkla_VS_Code/Nash Git/Marketplace/frontend/src/store/modules/auth.js[39m:1:1
[33m  1  |  import AuthService from '../../services/auth.service';
     |  ^
  2  |  
  3  |  const user = JSON.parse(localStorage.getItem('user'));[39m
      at TransformPluginContext._formatLog (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:42451:41)
      at TransformPluginContext.error (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:42448:16)
      at TransformPluginContext.transform (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:40378:14)
      at async EnvironmentPluginContainer.transform (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:42246:18)
      at async loadAndTransform (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:35698:27)
      at async viteTransformMiddleware (file:///D:/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/node_modules/vite/dist/node/chunks/dep-BMIURPaQ.js:37202:24)

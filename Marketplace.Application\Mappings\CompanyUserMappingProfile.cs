﻿using AutoMapper;
using Marketplace.Application.Commands.CompanyUser;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;

public class CompanyUserMappingProfile : Profile
{
    public CompanyUserMappingProfile()
    {

        CreateMap<StoreCompanyUserCommand, CompanyUser>()
            .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
            .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.UserId))
            .ForMember(dest => dest.IsOwner, opt => opt.MapFrom(src => src.IsOwner));

        CreateMap<CompanyUser, CompanyUserResponse>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
            .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.UserId))
            .ForMember(dest => dest.IsOwner, opt => opt.MapFrom(src => src.IsOwner))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow)) // Тимчасово використовуємо поточну дату
            .ForMember(dest => dest.User, opt => opt.MapFrom(src => src.User));


        //// Мапінг для оновлення CompanyUser (UpdateCompanyUserCommand -> CompanyUser)
        //CreateMap<UpdateCompanyUserCommand, CompanyUser>()
        //    .ForMember(dest => dest.IsOwner, opt => opt.MapFrom(src => src.IsOwner ?? dest.IsOwner));
    }
}

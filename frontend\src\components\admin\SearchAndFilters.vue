<template>
  <div class="search-and-filters">
    <div class="box">
      <div class="columns">
        <!-- Search -->
        <div class="column" :class="searchColumnClass">
          <div class="field">
            <label class="label">{{ searchLabel }}</label>
            <div class="control has-icons-left">
              <input
                type="text"
                class="input"
                :placeholder="searchPlaceholder"
                v-model="searchQuery"
                @input="onSearchChange"
              />
              <span class="icon is-small is-left">
                <i class="fas fa-search"></i>
              </span>
            </div>
          </div>
        </div>

        <!-- Dynamic Filters -->
        <div 
          v-for="(field, key) in filterFields" 
          :key="key"
          class="column is-3"
        >
          <div class="field">
            <label class="label">{{ field.label }}</label>
            <div class="control">
              <!-- Select Filter -->
              <div v-if="field.type === 'select'" class="select is-fullwidth">
                <select v-model="filters[key]" @change="onFilterChange">
                  <option value="">All {{ field.label }}</option>
                  <option 
                    v-for="option in field.options" 
                    :key="option.value" 
                    :value="option.value"
                  >
                    {{ option.label }}
                  </option>
                </select>
              </div>

              <!-- Date Filter -->
              <input 
                v-else-if="field.type === 'date'"
                type="date" 
                class="input" 
                v-model="filters[key]"
                @change="onFilterChange"
              />

              <!-- Number Filter -->
              <input 
                v-else-if="field.type === 'number'"
                type="number" 
                class="input" 
                :placeholder="field.placeholder"
                v-model="filters[key]"
                @input="onFilterChange"
              />

              <!-- Text Filter -->
              <input 
                v-else
                type="text" 
                class="input" 
                :placeholder="field.placeholder"
                v-model="filters[key]"
                @input="onFilterChange"
              />
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="column is-2">
          <div class="field">
            <label class="label">&nbsp;</label>
            <div class="control">
              <button 
                class="button is-light is-fullwidth"
                @click="onResetFilters"
                :disabled="loading"
              >
                <span class="icon">
                  <i class="fas fa-undo"></i>
                </span>
                <span>Reset</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Summary -->
      <div class="level" v-if="totalItems !== null">
        <div class="level-left">
          <div class="level-item">
            <p class="has-text-grey">
              <span v-if="loading">Loading...</span>
              <span v-else>
                Showing {{ totalItems }} {{ itemName }}
                <span v-if="searchQuery || hasActiveFilters">
                  (filtered)
                </span>
              </span>
            </p>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <slot name="actions"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({})
  },
  filterFields: {
    type: Object,
    default: () => ({})
  },
  searchLabel: {
    type: String,
    default: 'Search'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search...'
  },
  searchColumnClass: {
    type: String,
    default: 'is-4'
  },
  totalItems: {
    type: Number,
    default: null
  },
  itemName: {
    type: String,
    default: 'items'
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['search-changed', 'filter-changed', 'reset-filters']);

// Reactive data
const searchQuery = ref('');

// Computed
const hasActiveFilters = computed(() => {
  return Object.values(props.filters).some(value => value !== '' && value !== null && value !== undefined);
});

// Methods
const onSearchChange = () => {
  emit('search-changed', searchQuery.value);
};

const onFilterChange = () => {
  emit('filter-changed', props.filters);
};

const onResetFilters = () => {
  searchQuery.value = '';
  // Reset all filters
  Object.keys(props.filters).forEach(key => {
    props.filters[key] = '';
  });
  emit('reset-filters');
};
</script>

<style scoped>
.search-and-filters {
  margin-bottom: 1.5rem;
}

.level {
  margin-bottom: 0 !important;
}

.field {
  margin-bottom: 0.75rem;
}

.label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.input, .select select {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.input:focus, .select select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.125em rgba(59, 130, 246, 0.25);
}

.button.is-light {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.button.is-light:hover {
  background-color: var(--card-bg);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.icon {
  color: var(--text-secondary);
}
</style>

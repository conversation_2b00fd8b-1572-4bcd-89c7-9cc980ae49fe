﻿using Marketplace.Application.Commands.Company;
using Marketplace.Application.Commands.CompanySchedule;
using Marketplace.Application.Commands.File;
using Marketplace.Application.Commands.Meta;
using Marketplace.Application.Queries.Company;
using Marketplace.Application.Queries.CompanySchedule;
using Marketplace.Application.Queries.CompanyUser;
using Marketplace.Application.Queries.Product;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Admin;

[ApiController]
[Authorize(Roles = "Admin,Moderator")]
[Route("api/admin/companies")]
public class AdminCompanyController : BasicApiController
{
    private readonly IMediator _mediator;

    public AdminCompanyController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllCompanies(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        [FromQuery] bool? isFeatured = null,
        [FromQuery] bool? isApproved = null,
        CancellationToken cancellationToken = default)
    {
        // Для admin панелі показуємо всі компанії (схвалені та несхвалені)
        // Якщо isApproved не вказано, показуємо всі
        var query = new GetAllCompanyQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize,
            isApproved, // Дозволяємо фільтрувати за статусом схвалення
            isFeatured);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(ApiResponse<PaginatedResponse<CompanyResponse>>.SuccessWithData(response));
    }

    [HttpGet]
    [Route("{id}")]
    public async Task<IActionResult> GetCompanyById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCompanyQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(ApiResponse<CompanyResponse>.SuccessWithData(response)) : NotFound(ApiResponse.Failure("Company not found"));
    }

    [HttpGet]
    [Route("{id}/detailed")]
    public async Task<IActionResult> GetDetailedCompanyById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetDetailedCompanyQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(ApiResponse<DetailedCompanyResponse>.SuccessWithData(response)) : NotFound(ApiResponse.Failure("Company not found"));
    }

    [HttpPut]
    [Route("{id}/detailed")]
    public async Task<IActionResult> UpdateDetailedCompany([FromRoute] Guid id, [FromBody] UpdateDetailedCompanyCommand command, CancellationToken cancellationToken)
    {
        command.Id = id;
        var response = await _mediator.Send(command, cancellationToken);

        return Ok(ApiResponse<DetailedCompanyResponse>.SuccessWithData(response));
    }

    [HttpPost]
    public async Task<IActionResult> CreateCompany([FromBody] StoreCompanyCommand command, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(command, cancellationToken);

        return response != null ? CreatedAtAction(nameof(GetCompanyById), new { id = response }, new { id = response, success = true }) : BadRequest();
    }

    [HttpPut]
    [Route("{id}")]
    public async Task<IActionResult> UpdateCompany([FromRoute] Guid id, [FromBody] UpdateCompanyCommand command, CancellationToken cancellationToken)
    {
        // Переконуємося, що ID з маршруту використовується для команди
        var commandWithId = command with { Id = id };
        await _mediator.Send(commandWithId, cancellationToken);

        return Ok(new { id, success = true });
    }

    [HttpDelete]
    [Route("{id}")]
    public async Task<IActionResult> DeleteCompany([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var command = new DeleteCompanyCommand(id);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound();
    }

    [HttpGet]
    [Route("{id}/schedule")]
    public async Task<IActionResult> GetCompanySchedule([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCompanyScheduleByCompanyIdQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(response);
    }

    [HttpPost]
    [Route("{id}/schedule")]
    public async Task<IActionResult> CreateCompanySchedule([FromRoute] Guid id, [FromBody] StoreCompanyScheduleCommand command, CancellationToken cancellationToken)
    {
        // Переконуємося, що ID компанії з маршруту використовується для команди
        var commandWithCompanyId = command with { CompanyId = id };
        var response = await _mediator.Send(commandWithCompanyId, cancellationToken);

        return response != null ? Ok(new { id = response, success = true }) : BadRequest();
    }

    [HttpPut]
    [Route("{companyId}/schedule/{id}")]
    public async Task<IActionResult> UpdateCompanySchedule([FromRoute] Guid companyId, [FromRoute] Guid id, [FromBody] UpdateCompanyScheduleCommand command, CancellationToken cancellationToken)
    {
        // Переконуємося, що ID з маршруту використовується для команди
        var commandWithIds = command with { Id = id, CompanyId = companyId };
        await _mediator.Send(commandWithIds, cancellationToken);

        return Ok(new { id, success = true });
    }

    [HttpDelete]
    [Route("{companyId}/schedule/{id}")]
    public async Task<IActionResult> DeleteCompanySchedule([FromRoute] Guid companyId, [FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var command = new DeleteCompanyScheduleCommand(id, companyId);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound();
    }

    [HttpPost("{id}/images")]
    public async Task<IActionResult> UploadImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var companyQuery = new GetCompanyQuery(id);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено."));
        }

        // Створюємо команду для завантаження зображення
        var command = new UploadImageCommand("company", id, image);
        var result = await _mediator.Send(command, cancellationToken);

        return result != null
            ? Ok(ApiResponse<Guid>.SuccessWithData(result.Id, "Зображення успішно завантажено."))
            : BadRequest(ApiResponse.Failure("Не вдалося завантажити зображення."));
    }

    [HttpDelete("{companyId}/images/{imageId}")]
    public async Task<IActionResult> DeleteImage([FromRoute] Guid companyId, [FromRoute] Guid imageId, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var companyQuery = new GetCompanyQuery(companyId);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено."));
        }

        // Видаляємо зображення
        var command = new DeleteImageCommand("company", imageId);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Зображення не знайдено."));
    }

    [HttpPost("{id}/meta-image")]
    public async Task<IActionResult> UploadMetaImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи команія існує
        var companyQuery = new GetCompanyQuery(id);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
        {
            return NotFound();
        }

        // Зчитуємо файл у масив байтів
        using var memoryStream = new MemoryStream();
        await image.CopyToAsync(memoryStream, cancellationToken);
        var imageBytes = memoryStream.ToArray();

        // Створюємо команду для завантаження зображення
        var command = new UploadMetaImageCommand("company", id, image);
        //var command = new UploadCompanyMetaImageCommand(id, imageBytes, image.FileName, image.ContentType);
        var result = await _mediator.Send(command, cancellationToken);

        return result != null ? Ok(new { success = true }) : BadRequest();
    }

    [HttpDelete("{id}/meta-image")]
    public async Task<IActionResult> DeleteMetaImage([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи команія існує
        var companyQuery = new GetCompanyQuery(id);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
        {
            return NotFound();
        }

        // Видаляємо мета-зображення
        var command = new DeleteMetaImageCommand("company", id);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound();
    }

    // Moderator functionality for company approval/rejection
    [HttpGet("pending")]
    public async Task<IActionResult> GetPendingCompanies(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetPendingCompaniesQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<CompanyResponse>>.SuccessWithData(response));
    }

    [HttpPost("{id}/approve")]
    public async Task<IActionResult> ApproveCompany([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId()!.Value;

        var command = new ModeratorApproveCompanyCommand(id, userId);
        var result = await _mediator.Send(command, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Компанію успішно схвалено."))
            : NotFound(ApiResponse.Failure("Компанію не знайдено або не може бути схвалена."));
    }

    [HttpPost("{id}/reject")]
    public async Task<IActionResult> RejectCompany([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var command = new ModeratorRejectCompanyCommand(id);
        var result = await _mediator.Send(command, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Компанію успішно відхилено."))
            : NotFound(ApiResponse.Failure("Компанію не знайдено або не може бути відхилена."));
    }

    [HttpPost("bulk-approve")]
    public async Task<IActionResult> BulkApproveCompanies(
        [FromBody] ModeratorBulkApproveCompaniesCommand command,
        CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId()!.Value;

        var commandWithModeratorId = command with { ModeratorId = userId };
        var result = await _mediator.Send(commandWithModeratorId, cancellationToken);

        return Ok(ApiResponse<object>.SuccessWithData(
            new { approvedCount = result },
            $"Успішно схвалено {result} компаній."));
    }

    [HttpPost("bulk-reject")]
    public async Task<IActionResult> BulkRejectCompanies(
        [FromBody] ModeratorBulkRejectCompaniesCommand command,
        CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(command, cancellationToken);

        return Ok(ApiResponse<object>.SuccessWithData(
            new { rejectedCount = result },
            $"Успішно відхилено {result} компаній."));
    }

    [HttpGet("{id}/users")]
    public async Task<IActionResult> GetCompanyUsers(
        [FromRoute] Guid id,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetCompanyUsersByCompanyIdQuery(
            id,
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<CompanyUserResponse>>.SuccessWithData(response));
    }

    [HttpGet("{id}/products")]
    public async Task<IActionResult> GetCompanyProducts(
        [FromRoute] Guid id,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetProductsByCompanyQuery(
            id,
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
    }
}

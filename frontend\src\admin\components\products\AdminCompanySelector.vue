<template>
  <div v-if="isMounted" class="admin-company-selector">
    <label v-if="label" class="admin-form-label" :class="{ 'admin-form-label--required': required }">
      {{ label }}
    </label>

    <div class="admin-selector-container" :class="{ 'admin-selector-container--error': hasError }">
      <!-- Search Input -->
      <div class="admin-search-input-wrapper">
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          class="admin-search-input"
          :placeholder="placeholder"
          :disabled="disabled || loading"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeydown"
          autocomplete="off"
        />
        <div class="admin-search-input-icons">
          <i v-if="loading" class="fas fa-spinner fa-spin admin-search-icon"></i>
          <i v-else-if="selectedCompany && selectedCompany.id" class="fas fa-building admin-search-icon"></i>
          <i v-else class="fas fa-search admin-search-icon"></i>

          <button
            v-if="selectedCompany && selectedCompany.id && !disabled"
            type="button"
            class="admin-clear-btn"
            @click="clearSelection"
            title="Clear selection"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <!-- Dropdown -->
      <div v-if="showDropdown && isReady && isMounted" class="admin-dropdown">
        <div v-if="loading" class="admin-dropdown-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Loading companies...</span>
        </div>

        <div v-else-if="!filteredCompanies || filteredCompanies.length === 0" class="admin-dropdown-empty">
          <i class="fas fa-building"></i>
          <span>{{ searchQuery ? 'No companies found' : 'No companies available' }}</span>
        </div>

        <div v-else-if="filteredCompanies && filteredCompanies.length > 0" class="admin-dropdown-list">
          <button
            v-for="(company, index) in filteredCompanies"
            v-if="company && company.id"
            :key="company.id"
            type="button"
            class="admin-dropdown-item"
            :class="{ 'admin-dropdown-item--highlighted': index === highlightedIndex }"
            @click="selectCompany(company)"
            @mouseenter="highlightedIndex = index"
          >
            <div class="admin-company-item">
              <div class="admin-company-info">
                <div class="admin-company-name">{{ company.name || 'Unknown Company' }}</div>
                <div v-if="company.slug" class="admin-company-slug">{{ company.slug }}</div>
              </div>
              <div class="admin-company-status">
                <span
                  class="admin-status-badge"
                  :class="company.isApproved ? 'admin-status-badge--success' : 'admin-status-badge--warning'"
                >
                  <i class="fas" :class="company.isApproved ? 'fa-check' : 'fa-clock'"></i>
                  {{ company.isApproved ? 'Approved' : 'Pending' }}
                </span>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Selected Company Display -->
    <div v-if="selectedCompany && !showDropdown && isMounted" class="admin-selected-company">
      <div class="admin-company-card">
        <div class="admin-company-avatar">
          <i class="fas fa-building"></i>
        </div>
        <div class="admin-company-details">
          <div class="admin-company-name">{{ selectedCompany.name }}</div>
          <div class="admin-company-meta">
            <span v-if="selectedCompany.slug" class="admin-company-slug">{{ selectedCompany.slug }}</span>
            <span 
              class="admin-status-badge admin-status-badge--sm"
              :class="selectedCompany.isApproved ? 'admin-status-badge--success' : 'admin-status-badge--warning'"
            >
              {{ selectedCompany.isApproved ? 'Approved' : 'Pending' }}
            </span>
          </div>
        </div>
        <button
          v-if="!disabled"
          type="button"
          class="admin-company-remove"
          @click="clearSelection"
          title="Remove company"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
    
    <!-- Error Message -->
    <div v-if="errorMessage" class="admin-form-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ errorMessage }}
    </div>
    
    <!-- Help Text -->
    <div v-if="helpText" class="admin-form-help">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick, markRaw } from 'vue';
import { productsService } from '../../services/products.js';
import { wrapAsyncWithMountCheck, createSafeStateUpdater } from '@/utils/domSafety.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: 'Company'
  },
  placeholder: {
    type: String,
    default: 'Search for a company...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// Reactive data
const searchQuery = ref('');
const companies = ref([]);
const loading = ref(false);
const showDropdown = ref(false);
const highlightedIndex = ref(-1);
const searchInput = ref(null);

// AbortController for request cancellation
let abortController = null;

// Computed
const hasError = computed(() => !!props.errorMessage);

// Додаємо обчислювану властивість для перевірки готовності
const isReady = computed(() => {
  return !loading.value && Array.isArray(companies.value);
});

const selectedCompany = computed(() => {
  if (!props.modelValue || !isReady.value) return null;
  const companiesList = companies.value || [];
  return companiesList.find(company => company && company.id === props.modelValue);
});

const filteredCompanies = computed(() => {
  // Ensure companies.value is always an array and component is ready
  if (!isReady.value) return [];
  const companiesList = companies.value || [];

  if (!searchQuery.value.trim()) return companiesList;

  const query = searchQuery.value.toLowerCase();
  return companiesList.filter(company =>
    company && company.name && (
      company.name.toLowerCase().includes(query) ||
      (company.slug && company.slug.toLowerCase().includes(query))
    )
  );
});

// Methods
const loadCompanies = wrapAsyncWithMountCheck(async () => {
  try {
    // Check if component is still mounted
    if (!isMounted.value) {
      return;
    }

    // Cancel previous request if exists
    if (abortController) {
      abortController.abort();
    }

    // Create new AbortController
    abortController = new AbortController();

    loading.value = true;
    const response = await productsService.getCompanies({
      pageSize: 1000,
      signal: abortController.signal
    });

    // Check if component is still mounted after async operation
    if (!isMounted.value) {
      return;
    }

    // Ensure response is always an array and mark as raw to prevent deep reactivity
    if (Array.isArray(response)) {
      companies.value = markRaw(response);
    } else if (response && Array.isArray(response.data)) {
      companies.value = markRaw(response.data);
    } else {
      companies.value = markRaw([]);
    }
  } catch (error) {
    // Don't log canceled requests as errors
    if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
      console.log('Companies request was canceled');
      return;
    }

    console.error('Error loading companies:', error);

    // Only update state if component is still mounted
    if (isMounted.value) {
      companies.value = markRaw([]);
    }
  } finally {
    // Only update loading state if component is still mounted
    if (isMounted.value) {
      loading.value = false;
    }
    abortController = null;
  }
}, context);

const selectCompany = (company) => {
  if (!company || !company.id) {
    console.error('Invalid company selected:', company);
    return;
  }

  emit('update:modelValue', company.id);
  emit('change', company.id);
  emit('select', company);

  searchQuery.value = company.name || '';
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const clearSelection = () => {
  emit('update:modelValue', null);
  emit('change', null);
  emit('select', null);
  
  searchQuery.value = '';
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const handleFocus = () => {
  showDropdown.value = true;
  if (selectedCompany.value) {
    searchQuery.value = '';
  }
};

const handleBlur = () => {
  // Delay hiding dropdown to allow for clicks
  setTimeout(() => {
    showDropdown.value = false;
    if (selectedCompany.value) {
      searchQuery.value = selectedCompany.value.name;
    }
  }, 200);
};

const handleKeydown = (event) => {
  if (!showDropdown.value) return;
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredCompanies.value.length - 1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (highlightedIndex.value >= 0 && filteredCompanies.value[highlightedIndex.value]) {
        selectCompany(filteredCompanies.value[highlightedIndex.value]);
      }
      break;
    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      searchInput.value?.blur();
      break;
  }
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  if (newValue && selectedCompany.value && selectedCompany.value.name) {
    searchQuery.value = selectedCompany.value.name;
  } else {
    searchQuery.value = '';
  }
}, { immediate: false });

watch(searchQuery, () => {
  highlightedIndex.value = -1;
});

// Component mounted state
const isMounted = ref(false);

// Create safe state updater
const context = { get isMounted() { return isMounted.value; } };
const safeUpdate = createSafeStateUpdater(context);

// Lifecycle
onMounted(async () => {
  try {
    isMounted.value = true;
    await nextTick(); // Ensure DOM is ready

    // Check if component is still mounted before proceeding
    if (!isMounted.value) return;

    await loadCompanies();

    // Set initial search query if there's a selected company
    await nextTick(); // Wait for companies to be loaded

    // Check again if component is still mounted
    if (isMounted.value && props.modelValue && selectedCompany.value && selectedCompany.value.name) {
      searchQuery.value = selectedCompany.value.name;
    }
  } catch (error) {
    console.error('Error in AdminCompanySelector onMounted:', error);
  }
});

// Cleanup on unmount
onBeforeUnmount(() => {
  isMounted.value = false;
  showDropdown.value = false;

  if (abortController) {
    abortController.abort();
    abortController = null;
  }
});

onUnmounted(() => {
  if (abortController) {
    abortController.abort();
    abortController = null;
  }
});
</script>

<style scoped>
.admin-company-selector {
  position: relative;
  width: 100%;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-selector-container {
  position: relative;
}

.admin-selector-container--error .admin-search-input {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-search-input-wrapper {
  position: relative;
}

.admin-search-input {
  width: 100%;
  padding: var(--admin-space-sm) var(--admin-space-xl) var(--admin-space-sm) var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-search-input:disabled {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
  cursor: not-allowed;
}

.admin-search-input-icons {
  position: absolute;
  right: var(--admin-space-sm);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-search-icon {
  color: var(--admin-text-muted);
  font-size: var(--admin-text-sm);
}

.admin-clear-btn {
  background: none;
  border: none;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-clear-btn:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-danger);
}

/* Dropdown */
.admin-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  box-shadow: var(--admin-shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  margin-top: var(--admin-space-xs);
}

.admin-dropdown-loading,
.admin-dropdown-empty {
  padding: var(--admin-space-lg);
  text-align: center;
  color: var(--admin-text-muted);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-dropdown-list {
  padding: var(--admin-space-xs);
}

.admin-dropdown-item {
  width: 100%;
  padding: var(--admin-space-sm);
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  border-radius: var(--admin-radius-sm);
  transition: background-color var(--admin-transition-base);
}

.admin-dropdown-item:hover,
.admin-dropdown-item--highlighted {
  background: var(--admin-bg-secondary);
}

.admin-company-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--admin-space-sm);
}

.admin-company-info {
  flex: 1;
  min-width: 0;
}

.admin-company-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-company-slug {
  font-size: var(--admin-text-xs);
  color: var(--admin-text-muted);
  font-family: var(--admin-font-mono);
}

.admin-company-status {
  flex-shrink: 0;
}

/* Selected Company */
.admin-selected-company {
  margin-top: var(--admin-space-sm);
}

.admin-company-card {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-space-sm);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-company-avatar {
  width: 40px;
  height: 40px;
  background: var(--admin-primary);
  color: var(--admin-text-white);
  border-radius: var(--admin-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--admin-text-lg);
  flex-shrink: 0;
}

.admin-company-details {
  flex: 1;
  min-width: 0;
}

.admin-company-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  margin-top: var(--admin-space-xs);
}

.admin-company-remove {
  background: none;
  border: none;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  flex-shrink: 0;
}

.admin-company-remove:hover {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

/* Status Badge */
.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-badge--sm {
  padding: 2px var(--admin-space-xs);
  font-size: 10px;
}

.admin-status-badge--success {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-status-badge--warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

/* Form Messages */
.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-danger);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-company-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-xs);
  }
  
  .admin-company-status {
    align-self: flex-start;
  }
}
</style>

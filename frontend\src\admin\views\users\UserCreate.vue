<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Users
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-user-plus admin-page-icon"></i>
          Create New User
        </h1>
        <p class="admin-page-subtitle">Create a new user account with role and permissions</p>
      </div>
      <div class="admin-page-actions">
        <button @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button @click="saveUser" :disabled="saving" class="admin-btn admin-btn-primary">
          <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': saving }"></i>
          {{ saving ? 'Creating...' : 'Create User' }}
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error creating user</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="error = null" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-times"></i>
        Dismiss
      </button>
    </div>

    <div class="admin-user-create-content">
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-user-circle"></i>
            User Information
          </h3>
        </div>
        <div class="admin-card-content">
          <form @submit.prevent="saveUser" class="admin-user-form">
            <!-- Basic Information -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Basic Information</h4>
              
              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Username *
                  </label>
                  <input 
                    class="admin-form-input" 
                    type="text" 
                    placeholder="Enter username" 
                    v-model="form.username"
                    :class="{ 'admin-form-input-error': errors.username }"
                    required>
                  <div v-if="errors.username" class="admin-form-error">{{ errors.username }}</div>
                </div>
                
                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Email *
                  </label>
                  <input 
                    class="admin-form-input" 
                    type="email" 
                    placeholder="Enter email address" 
                    v-model="form.email"
                    :class="{ 'admin-form-input-error': errors.email }"
                    required>
                  <div v-if="errors.email" class="admin-form-error">{{ errors.email }}</div>
                </div>
              </div>

              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Role *
                  </label>
                  <select 
                    class="admin-form-select" 
                    v-model="form.role" 
                    :class="{ 'admin-form-input-error': errors.role }"
                    required>
                    <option value="">Select role</option>
                    <option value="Buyer">Buyer</option>
                    <option value="Seller">Seller</option>
                    <option value="SellerOwner">Seller Owner</option>
                    <option value="Moderator">Moderator</option>
                    <option value="Admin">Admin</option>
                  </select>
                  <div v-if="errors.role" class="admin-form-error">{{ errors.role }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Birthday
                  </label>
                  <input 
                    class="admin-form-input" 
                    type="date" 
                    v-model="form.birthday"
                    :class="{ 'admin-form-input-error': errors.birthday }">
                  <div class="admin-form-hint">Optional - Date of birth</div>
                  <div v-if="errors.birthday" class="admin-form-error">{{ errors.birthday }}</div>
                </div>
              </div>
            </div>

            <!-- Password Section -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Password</h4>
              <div class="admin-form-group">
                <label class="admin-form-label admin-required">
                  Password *
                </label>
                <input 
                  class="admin-form-input" 
                  type="password" 
                  placeholder="Enter password" 
                  v-model="form.password"
                  :class="{ 'admin-form-input-error': errors.password }"
                  required>
                <div class="admin-form-hint">
                  Password must be at least 8 characters with uppercase letter, number and special character (!@#$%^&*)
                </div>
                <div v-if="errors.password" class="admin-form-error">{{ errors.password }}</div>
              </div>
            </div>

            <!-- Form Legend -->
            <div class="admin-form-legend">
              <p class="admin-form-legend-text">
                <span class="admin-required-indicator">*</span> - обов'язкові поля
              </p>
            </div>

            <!-- Form Actions -->
            <div class="admin-form-actions">
              <button type="submit" class="admin-btn admin-btn-primary" :disabled="saving">
                <span v-if="saving">
                  <i class="fas fa-spinner fa-spin"></i>
                  <span>Creating...</span>
                </span>
                <span v-else>
                  <i class="fas fa-save"></i>
                  <span>Create User</span>
                </span>
              </button>
              <router-link to="/admin/users" class="admin-btn admin-btn-secondary">
                <i class="fas fa-times"></i>
                <span>Cancel</span>
              </router-link>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';

const router = useRouter();

// State
const saving = ref(false);
const error = ref(null);

// Form data
const form = reactive({
  email: '',
  username: '',
  password: '',
  role: '',
  birthday: ''
});

// Form errors
const errors = reactive({
  email: '',
  username: '',
  password: '',
  role: '',
  birthday: ''
});

// Validation
const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });
};

const validateForm = () => {
  clearErrors();
  let isValid = true;

  // Username validation
  if (!form.username.trim()) {
    errors.username = 'Username is required';
    isValid = false;
  } else if (form.username.length < 3) {
    errors.username = 'Username must be at least 3 characters';
    isValid = false;
  }

  // Email validation
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Please enter a valid email address';
    isValid = false;
  }

  // Password validation (required for new users)
  if (!form.password.trim()) {
    errors.password = 'Password is required';
    isValid = false;
  } else if (form.password.length < 8) {
    errors.password = 'Password must be at least 8 characters';
    isValid = false;
  } else if (!/[A-Z]/.test(form.password)) {
    errors.password = 'Password must contain at least one uppercase letter';
    isValid = false;
  } else if (!/[0-9]/.test(form.password)) {
    errors.password = 'Password must contain at least one number';
    isValid = false;
  } else if (!/[!@#$%^&*]/.test(form.password)) {
    errors.password = 'Password must contain at least one special character (!@#$%^&*)';
    isValid = false;
  }

  // Role validation
  if (!form.role) {
    errors.role = 'Role is required';
    isValid = false;
  }

  return isValid;
};

// Methods
const saveUser = async () => {
  // Validate form first
  if (!validateForm()) {
    return;
  }

  saving.value = true;
  error.value = null;
  clearErrors();

  try {
    const userData = { ...form };

    // Convert birthday to proper format or remove if empty
    if (!userData.birthday) {
      delete userData.birthday;
    } else {
      // Convert date string to ISO format for backend
      userData.birthday = new Date(userData.birthday).toISOString();
    }

    const result = await usersService.createUser(userData);

    // Redirect to user detail page if created successfully
    if (result && result.id) {
      router.push(`/admin/users/${result.id}`);
    } else {
      router.push('/admin/users');
    }
  } catch (err) {
    console.error('Error creating user:', err);
    
    // Handle validation errors from server
    if (err.response && err.response.data && err.response.data.errors) {
      const serverErrors = err.response.data.errors;
      Object.keys(serverErrors).forEach(field => {
        if (field.toLowerCase() in errors) {
          errors[field.toLowerCase()] = serverErrors[field][0] || serverErrors[field];
        }
      });
    } else {
      error.value = err.response?.data?.message || 'Failed to create user. Please try again.';
    }
  } finally {
    saving.value = false;
  }
};

const goBack = () => {
  router.push('/admin/users');
};

const resetForm = () => {
  clearErrors();
  
  // Reset to empty form
  form.email = '';
  form.username = '';
  form.password = '';
  form.role = '';
  form.birthday = '';
};
</script>

<style scoped>
.admin-user-create-content {
  max-width: 800px;
  margin: 0 auto;
}

.admin-user-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-2xl);
}

.admin-form-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-form-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

/* Required field styles */
.admin-required {
  position: relative;
}

.admin-required::after {
  content: ' *';
  color: var(--admin-danger);
  font-weight: bold;
}

.admin-required-indicator {
  color: var(--admin-danger);
  font-weight: bold;
}

/* Form validation styles */
.admin-form-input-error {
  border-color: var(--admin-danger) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.admin-form-error {
  color: var(--admin-danger);
  font-size: var(--admin-text-sm);
  margin-top: var(--admin-space-xs);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-error::before {
  content: '⚠';
  font-size: var(--admin-text-xs);
}

.admin-form-hint {
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
  margin-top: var(--admin-space-xs);
  font-style: italic;
}

/* Form legend */
.admin-form-legend {
  padding: var(--admin-space-md);
  background-color: var(--admin-bg-secondary);
  border-radius: var(--admin-radius);
  border-left: 4px solid var(--admin-primary);
}

.admin-form-legend-text {
  margin: 0;
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}
</style>

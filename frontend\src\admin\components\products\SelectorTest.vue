<template>
  <div class="selector-test">
    <h2>Selector Test Page</h2>
    
    <div class="test-section">
      <h3>Company Selector Test</h3>
      <SimpleCompanySelector
        v-model="selectedCompany"
        label="Test Company"
        placeholder="Choose a company..."
        :required="true"
        help-text="This is a test of the company selector"
        @change="onCompanyChange"
      />
      <p>Selected Company ID: {{ selectedCompany || 'None' }}</p>
    </div>
    
    <div class="test-section">
      <h3>Category Selector Test</h3>
      <SimpleCategorySelector
        v-model="selectedCategory"
        label="Test Category"
        placeholder="Choose a category..."
        :required="true"
        help-text="This is a test of the category selector"
        @change="onCategoryChange"
      />
      <p>Selected Category ID: {{ selectedCategory || 'None' }}</p>
    </div>

    <div class="test-section">
      <h3>Product Attributes Test</h3>
      <SimpleProductAttributesEditor
        v-model="testAttributes"
        @change="onAttributesChange"
      />
      <p>Attributes: {{ JSON.stringify(testAttributes, null, 2) }}</p>
    </div>

    <div class="test-section">
      <h3>Image Uploader Test (Multiple)</h3>
      <SimpleImageUploader
        v-model="testImages"
        title="Test Images"
        :multiple="true"
        :max-files="5"
        @upload="onImagesUpload"
        @remove="onImageRemove"
      />
      <p>Images count: {{ testImages.length }}</p>
    </div>

    <div class="test-section">
      <h3>Meta Image Test (Single)</h3>
      <SimpleImageUploader
        v-model="testMetaImage"
        title="Meta Image"
        :multiple="false"
        :max-files="1"
        @upload="onMetaImageUpload"
        @remove="onMetaImageRemove"
      />
      <p>Meta image: {{ testMetaImage.length > 0 ? 'Set' : 'Not set' }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SimpleCompanySelector from './SimpleCompanySelector.vue';
import SimpleCategorySelector from './SimpleCategorySelector.vue';
import SimpleProductAttributesEditor from './SimpleProductAttributesEditor.vue';
import SimpleImageUploader from './SimpleImageUploader.vue';

const selectedCompany = ref(null);
const selectedCategory = ref(null);
const testAttributes = ref({});
const testImages = ref([]);
const testMetaImage = ref([]);

const onCompanyChange = (value) => {
  console.log('Company changed:', value);
};

const onCategoryChange = (value) => {
  console.log('Category changed:', value);
};

const onAttributesChange = (attributes) => {
  console.log('Attributes changed:', attributes);
};

const onImagesUpload = (files) => {
  console.log('Images uploaded:', files);
};

const onImageRemove = (image, index) => {
  console.log('Image removed:', image, index);
};

const onMetaImageUpload = (files) => {
  console.log('Meta image uploaded:', files);
};

const onMetaImageRemove = (image, index) => {
  console.log('Meta image removed:', image, index);
};
</script>

<style scoped>
.selector-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

h2, h3 {
  margin-bottom: 1rem;
}

p {
  margin-top: 1rem;
  font-weight: bold;
}
</style>

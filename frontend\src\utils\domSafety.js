/**
 * DOM Safety Utilities
 * Provides utilities for safe DOM manipulation and component lifecycle management
 */

/**
 * Safely execute a function that might manipulate the DOM
 * @param {Function} fn - Function to execute
 * @param {Object} context - Context object with isMounted flag
 * @param {*} fallback - Fallback value if execution fails
 * @returns {*} Result of function execution or fallback
 */
export function safeDOMOperation(fn, context = {}, fallback = null) {
  try {
    // Check if context indicates component is mounted
    if (context.isMounted !== undefined && !context.isMounted) {
      console.warn('DOM operation skipped: component not mounted');
      return fallback;
    }

    // Check if DOM is available
    if (typeof document === 'undefined') {
      console.warn('DOM operation skipped: document not available');
      return fallback;
    }

    return fn();
  } catch (error) {
    // Check if this is a DOM manipulation error
    const isDOMError = error.message && (
      error.message.includes('insertBefore') ||
      error.message.includes('Cannot read properties of null') ||
      error.message.includes('Cannot set properties of null') ||
      error.message.includes('appendChild') ||
      error.message.includes('removeChild') ||
      error.message.includes('__vnode') ||
      error.message.includes('patchElement') ||
      error.message.includes('mountElement')
    );

    if (isDOMError) {
      console.warn('DOM operation failed safely:', error.message);
      return fallback;
    }

    // Re-throw non-DOM errors
    throw error;
  }
}

/**
 * Create a safe reactive state updater
 * @param {Object} context - Context object with isMounted flag
 * @returns {Function} Safe state updater function
 */
export function createSafeStateUpdater(context) {
  return function safeUpdate(updateFn) {
    if (context.isMounted === false) {
      console.warn('State update skipped: component not mounted');
      return;
    }

    try {
      updateFn();
    } catch (error) {
      console.error('Safe state update failed:', error);
    }
  };
}

/**
 * Wrap an async function to check mount status before and after execution
 * @param {Function} asyncFn - Async function to wrap
 * @param {Object} context - Context object with isMounted flag
 * @returns {Function} Wrapped async function
 */
export function wrapAsyncWithMountCheck(asyncFn, context) {
  return async function wrappedAsync(...args) {
    // Check before execution
    if (context.isMounted === false) {
      console.warn('Async operation skipped: component not mounted (before)');
      return;
    }

    try {
      const result = await asyncFn.apply(this, args);

      // Check after execution
      if (context.isMounted === false) {
        console.warn('Async operation result discarded: component unmounted during execution');
        return;
      }

      return result;
    } catch (error) {
      // Only log error if component is still mounted
      if (context.isMounted !== false) {
        console.error('Wrapped async operation failed:', error);
      }
      throw error;
    }
  };
}

/**
 * Create a debounced function that respects component mount status
 * @param {Function} fn - Function to debounce
 * @param {number} delay - Debounce delay in milliseconds
 * @param {Object} context - Context object with isMounted flag
 * @returns {Function} Debounced function
 */
export function createSafeDebounce(fn, delay, context) {
  let timeoutId = null;

  return function debouncedFn(...args) {
    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Check if component is mounted
    if (context.isMounted === false) {
      console.warn('Debounced function skipped: component not mounted');
      return;
    }

    timeoutId = setTimeout(() => {
      // Double-check mount status before execution
      if (context.isMounted !== false) {
        try {
          fn.apply(this, args);
        } catch (error) {
          console.error('Debounced function failed:', error);
        }
      }
      timeoutId = null;
    }, delay);
  };
}

/**
 * Cleanup function for component unmounting
 * @param {Object} context - Context object to clean up
 */
export function cleanupComponent(context) {
  if (context.isMounted !== undefined) {
    context.isMounted = false;
  }

  // Clear any pending timeouts
  if (context.timeouts) {
    context.timeouts.forEach(id => clearTimeout(id));
    context.timeouts.clear();
  }

  // Clear any intervals
  if (context.intervals) {
    context.intervals.forEach(id => clearInterval(id));
    context.intervals.clear();
  }

  // Abort any pending requests
  if (context.abortController) {
    context.abortController.abort();
    context.abortController = null;
  }
}

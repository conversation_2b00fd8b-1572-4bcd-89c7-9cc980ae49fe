<template>
  <div class="rating-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Ratings Management</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              class="button is-danger"
              @click="bulkDelete"
              :disabled="selectedRatings.length === 0 || actionLoading"
              v-if="selectedRatings.length > 0"
            >
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
              <span>Delete Selected ({{ selectedRatings.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Ratings"
      search-placeholder="Search by product name, user name, or comment..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="ratings"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading ratings...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchRatings">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Ratings Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th>Product</th>
                <th>User</th>
                <th>Service</th>
                <th>Delivery</th>
                <th>Accuracy</th>
                <th>Average</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="rating in ratings" :key="rating.id">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="rating.id"
                      v-model="selectedRatings"
                    />
                  </label>
                </td>
                <td>
                  <strong>{{ rating.productName || 'Unknown Product' }}</strong>
                </td>
                <td>{{ rating.userName || 'Unknown User' }}</td>
                <td>
                  <div class="stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.service }">
                      ★
                    </span>
                    <span class="ml-1">{{ rating.service }}</span>
                  </div>
                </td>
                <td>
                  <div class="stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.deliveryTime }">
                      ★
                    </span>
                    <span class="ml-1">{{ rating.deliveryTime }}</span>
                  </div>
                </td>
                <td>
                  <div class="stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.accuracy }">
                      ★
                    </span>
                    <span class="ml-1">{{ rating.accuracy }}</span>
                  </div>
                </td>
                <td>
                  <div class="stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.average }">
                      ★
                    </span>
                    <span class="ml-1">{{ rating.average.toFixed(1) }}</span>
                  </div>
                </td>
                <td>{{ formatDate(rating.createdAt) }}</td>
                <td>
                  <div class="buttons">
                    <button
                      class="button is-small is-danger"
                      @click="deleteRating(rating.id)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                      <span>Delete</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ratingsService } from '@/admin/services/ratings';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/components/admin/Pagination.vue';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Filter configuration
const filterFields = [
  {
    key: 'minRating',
    label: 'Min Rating',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1 Star' },
      { value: '2', label: '2 Stars' },
      { value: '3', label: '3 Stars' },
      { value: '4', label: '4 Stars' },
      { value: '5', label: '5 Stars' }
    ]
  },
  {
    key: 'maxRating',
    label: 'Max Rating',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1 Star' },
      { value: '2', label: '2 Stars' },
      { value: '3', label: '3 Stars' },
      { value: '4', label: '4 Stars' },
      { value: '5', label: '5 Stars' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'CreatedAt', label: 'Date Created' },
      { value: 'UpdatedAt', label: 'Date Updated' },
      { value: 'Service', label: 'Service Rating' },
      { value: 'DeliveryTime', label: 'Delivery Rating' },
      { value: 'Accuracy', label: 'Accuracy Rating' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items: ratingsData,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: ratingsService.getRatings,
  defaultFilters: {
    minRating: '',
    maxRating: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Transform ratings data to include average
const ratings = computed(() => {
  return ratingsData.value.map(rating => ({
    ...rating,
    average: (rating.service + rating.deliveryTime + rating.accuracy) / 3
  }));
});

// Additional reactive data
const actionLoading = ref(false);
const selectedRatings = ref([]);

// Computed
const allSelected = computed(() => {
  return ratings.value.length > 0 && selectedRatings.value.length === ratings.value.length;
});

const someSelected = computed(() => {
  return selectedRatings.value.length > 0 && selectedRatings.value.length < ratings.value.length;
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = filterFields.find(f => f.key === key)?.options?.[0]?.value || '';
    }
  });
  fetchData(1);
};

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedRatings.value = [];
  } else {
    selectedRatings.value = ratings.value.map(rating => rating.id);
  }
};

const deleteRating = async (id) => {
  if (!confirm('Are you sure you want to delete this rating?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await ratingsService.deleteRating(id);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete rating';
  } finally {
    actionLoading.value = false;
  }
};

const bulkDelete = async () => {
  if (!confirm(`Are you sure you want to delete ${selectedRatings.value.length} selected ratings?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    await ratingsService.bulkDeleteRatings(selectedRatings.value);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete ratings';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};
</script>

<style scoped>
.rating-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--darker-bg);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1em;
}

.star.is-filled {
  color: #ffd700;
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.button.is-danger:hover {
  background-color: var(--danger-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}
</style>

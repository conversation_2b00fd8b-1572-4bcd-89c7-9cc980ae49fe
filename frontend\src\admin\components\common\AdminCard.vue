<template>
  <div class="admin-card" :class="cardClasses">
    <div v-if="title || $slots.header" class="admin-card-header">
      <slot name="header">
        <h3 class="admin-card-title">{{ title }}</h3>
        <div v-if="subtitle" class="admin-card-subtitle">{{ subtitle }}</div>
      </slot>
      <div v-if="$slots.actions" class="admin-card-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <div class="admin-card-body" :class="bodyClasses">
      <slot></slot>
    </div>
    
    <div v-if="$slots.footer" class="admin-card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['sm', 'default', 'lg'].includes(value)
  },
  shadow: {
    type: String,
    default: 'default',
    validator: (value) => ['none', 'sm', 'default', 'lg'].includes(value)
  },
  padding: {
    type: String,
    default: 'default',
    validator: (value) => ['none', 'sm', 'default', 'lg'].includes(value)
  },
  hoverable: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Computed
const cardClasses = computed(() => ({
  [`admin-card--${props.variant}`]: props.variant !== 'default',
  [`admin-card--${props.size}`]: props.size !== 'default',
  [`admin-card--shadow-${props.shadow}`]: props.shadow !== 'default',
  'admin-card--hoverable': props.hoverable,
  'admin-card--loading': props.loading
}));

const bodyClasses = computed(() => ({
  [`admin-card-body--padding-${props.padding}`]: props.padding !== 'default'
}));
</script>

<style scoped>
.admin-card {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  transition: all var(--admin-transition-base);
  position: relative;
}

.admin-card--shadow-sm {
  box-shadow: var(--admin-shadow-sm);
}

.admin-card--shadow-default {
  box-shadow: var(--admin-shadow-md);
}

.admin-card--shadow-lg {
  box-shadow: var(--admin-shadow-lg);
}

.admin-card--hoverable:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.admin-card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.admin-card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Header */
.admin-card-header {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--admin-space-md);
}

.admin-card-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0;
  line-height: 1.4;
}

.admin-card-subtitle {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
  margin-top: var(--admin-space-xs);
}

.admin-card-actions {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  flex-shrink: 0;
}

/* Body */
.admin-card-body {
  padding: var(--admin-space-lg);
}

.admin-card-body--padding-none {
  padding: 0;
}

.admin-card-body--padding-sm {
  padding: var(--admin-space-md);
}

.admin-card-body--padding-lg {
  padding: var(--admin-space-xl);
}

/* Footer */
.admin-card-footer {
  padding: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
}

/* Size variants */
.admin-card--sm .admin-card-header,
.admin-card--sm .admin-card-body,
.admin-card--sm .admin-card-footer {
  padding: var(--admin-space-md);
}

.admin-card--lg .admin-card-header,
.admin-card--lg .admin-card-body,
.admin-card--lg .admin-card-footer {
  padding: var(--admin-space-xl);
}

/* Color variants */
.admin-card--primary {
  border-color: var(--admin-primary);
}

.admin-card--primary .admin-card-header {
  background: var(--admin-primary);
  color: var(--admin-text-white);
  border-bottom-color: var(--admin-primary);
}

.admin-card--primary .admin-card-title {
  color: var(--admin-text-white);
}

.admin-card--success {
  border-color: var(--admin-success);
}

.admin-card--success .admin-card-header {
  background: var(--admin-success);
  color: var(--admin-text-white);
  border-bottom-color: var(--admin-success);
}

.admin-card--success .admin-card-title {
  color: var(--admin-text-white);
}

.admin-card--warning {
  border-color: var(--admin-warning);
}

.admin-card--warning .admin-card-header {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
  border-bottom-color: var(--admin-warning);
}

.admin-card--danger {
  border-color: var(--admin-danger);
}

.admin-card--danger .admin-card-header {
  background: var(--admin-danger);
  color: var(--admin-text-white);
  border-bottom-color: var(--admin-danger);
}

.admin-card--danger .admin-card-title {
  color: var(--admin-text-white);
}

.admin-card--info {
  border-color: var(--admin-info);
}

.admin-card--info .admin-card-header {
  background: var(--admin-info);
  color: var(--admin-text-white);
  border-bottom-color: var(--admin-info);
}

.admin-card--info .admin-card-title {
  color: var(--admin-text-white);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-sm);
  }
  
  .admin-card-actions {
    justify-content: flex-end;
  }
  
  .admin-card-body {
    padding: var(--admin-space-md);
  }
}
</style>

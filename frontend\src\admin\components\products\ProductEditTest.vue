<template>
  <div class="product-edit-test">
    <h1>Product Edit Test Page</h1>
    
    <div class="test-section">
      <h2>Test ProductCreate Component</h2>
      <button @click="showCreateTest = !showCreateTest" class="test-btn">
        {{ showCreateTest ? 'Hide' : 'Show' }} ProductCreate Test
      </button>
      
      <div v-if="showCreateTest" class="test-container">
        <ProductCreate 
          @save="handleSave"
          @cancel="handleCancel"
          @created="handleCreated"
        />
      </div>
    </div>
    
    <div class="test-section">
      <h2>Test ProductEdit Component</h2>
      <div class="test-controls">
        <input 
          v-model="testProductId" 
          placeholder="Enter Product ID for edit test"
          class="test-input"
        />
        <button @click="showEditTest = !showEditTest" class="test-btn">
          {{ showEditTest ? 'Hide' : 'Show' }} ProductEdit Test
        </button>
      </div>
      
      <div v-if="showEditTest && testProductId" class="test-container">
        <ProductEdit 
          :product-id="testProductId"
          :is-create="false"
          @save="handleSave"
          @cancel="handleCancel"
          @updated="handleUpdated"
        />
      </div>
    </div>
    
    <div class="test-section">
      <h2>Test Results</h2>
      <div class="test-results">
        <h3>Console Output:</h3>
        <div class="console-output">
          <div v-for="(log, index) in consoleLogs" :key="index" class="log-entry">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type" :class="`log-${log.type}`">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="test-btn">Clear Logs</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ProductCreate from './ProductCreate.vue';
import ProductEdit from './ProductEdit.vue';

// Reactive data
const showCreateTest = ref(false);
const showEditTest = ref(false);
const testProductId = ref('');
const consoleLogs = ref([]);

// Methods
const addLog = (type, message) => {
  consoleLogs.value.push({
    time: new Date().toLocaleTimeString(),
    type,
    message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message
  });
  
  // Keep only last 50 logs
  if (consoleLogs.value.length > 50) {
    consoleLogs.value = consoleLogs.value.slice(-50);
  }
};

const handleSave = (productData) => {
  addLog('info', `Product saved: ${JSON.stringify(productData, null, 2)}`);
  console.log('Product saved:', productData);
};

const handleCreated = (productData) => {
  addLog('success', `Product created: ${JSON.stringify(productData, null, 2)}`);
  console.log('Product created:', productData);
};

const handleUpdated = (productData) => {
  addLog('success', `Product updated: ${JSON.stringify(productData, null, 2)}`);
  console.log('Product updated:', productData);
};

const handleCancel = () => {
  addLog('info', 'Operation cancelled');
  console.log('Operation cancelled');
};

const clearLogs = () => {
  consoleLogs.value = [];
};

// Override console methods to capture logs
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.log = (...args) => {
  addLog('info', args.join(' '));
  originalConsoleLog.apply(console, args);
};

console.error = (...args) => {
  addLog('error', args.join(' '));
  originalConsoleError.apply(console, args);
};

console.warn = (...args) => {
  addLog('warning', args.join(' '));
  originalConsoleWarn.apply(console, args);
};
</script>

<style scoped>
.product-edit-test {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
}

.test-section h2 {
  margin-top: 0;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.test-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.test-input {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 250px;
}

.test-btn {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #0056b3;
}

.test-container {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
}

.test-results {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
}

.console-output {
  max-height: 400px;
  overflow-y: auto;
  background: #212529;
  color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.log-entry {
  display: block;
  margin-bottom: 0.25rem;
  word-wrap: break-word;
}

.log-time {
  color: #6c757d;
  margin-right: 0.5rem;
}

.log-type {
  font-weight: bold;
  margin-right: 0.5rem;
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  font-size: 0.75rem;
}

.log-info {
  background: #17a2b8;
  color: white;
}

.log-success {
  background: #28a745;
  color: white;
}

.log-warning {
  background: #ffc107;
  color: #212529;
}

.log-error {
  background: #dc3545;
  color: white;
}

.log-message {
  white-space: pre-wrap;
}
</style>

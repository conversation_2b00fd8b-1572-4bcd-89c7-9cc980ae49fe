<template>
  <div>
    <div v-if="error" class="error-boundary">
      <div class="error-container">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h2 class="error-title">Something went wrong</h2>
        <p class="error-message">{{ errorMessage }}</p>
        <div class="error-actions">
          <button class="button is-primary" @click="retry">
            <span class="icon"><i class="fas fa-sync-alt"></i></span>
            <span>Try Again</span>
          </button>
          <button class="button is-light" @click="reset">
            <span class="icon"><i class="fas fa-home"></i></span>
            <span>Go to Dashboard</span>
          </button>
        </div>
        <div v-if="showDetails" class="error-details">
          <pre>{{ errorDetails }}</pre>
        </div>
        <button class="button is-small is-text" @click="toggleDetails">
          {{ showDetails ? 'Hide Details' : 'Show Details' }}
        </button>
      </div>
    </div>
    <slot v-else></slot>
  </div>
</template>

<script setup>
import { ref, computed, onErrorCaptured, provide } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  fallback: {
    type: Function,
    default: null
  }
});

const router = useRouter();
const error = ref(null);
const errorInfo = ref(null);
const showDetails = ref(false);

const errorMessage = computed(() => {
  if (!error.value) return '';
  
  // Provide a user-friendly message based on error type
  if (error.value.message?.includes('Network Error')) {
    return 'Network error. Please check your internet connection and try again.';
  }
  
  if (error.value.response?.status === 404) {
    return 'The requested resource was not found.';
  }
  
  if (error.value.response?.status === 403) {
    return 'You do not have permission to access this resource.';
  }
  
  if (error.value.response?.status === 401) {
    return 'Your session has expired. Please log in again.';
  }
  
  if (error.value.response?.status >= 500) {
    return 'Server error. Please try again later.';
  }
  
  return error.value.message || 'An unexpected error occurred.';
});

const errorDetails = computed(() => {
  if (!error.value) return '';
  
  let details = '';
  
  if (error.value.stack) {
    details += `Error Stack:\n${error.value.stack}\n\n`;
  }
  
  if (errorInfo.value) {
    details += `Component: ${errorInfo.value.component}\n`;
    details += `Props: ${JSON.stringify(errorInfo.value.props, null, 2)}\n`;
  }
  
  if (error.value.response) {
    details += `Response Status: ${error.value.response.status}\n`;
    details += `Response Data: ${JSON.stringify(error.value.response.data, null, 2)}\n`;
  }
  
  return details;
});

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

const retry = () => {
  error.value = null;
  errorInfo.value = null;
  
  // If a specific retry function is provided, call it
  if (props.fallback && typeof props.fallback === 'function') {
    props.fallback();
  } else {
    // Otherwise, just reload the current route
    router.go(0);
  }
};

const reset = () => {
  error.value = null;
  errorInfo.value = null;
  router.push('/admin/dashboard');
};

// Capture errors from child components
onErrorCaptured((err, instance, info) => {
  console.error('Error captured by boundary:', err);

  // Check if this is a DOM manipulation error that we can handle gracefully
  const isDOMError = err.message && (
    err.message.includes('insertBefore') ||
    err.message.includes('Cannot read properties of null') ||
    err.message.includes('Cannot read property') ||
    err.message.includes('Cannot set properties of null') ||
    err.message.includes('appendChild') ||
    err.message.includes('removeChild') ||
    err.message.includes('__vnode') ||
    err.message.includes('patchElement') ||
    err.message.includes('mountElement')
  );

  if (isDOMError) {
    console.warn('DOM manipulation error caught and handled gracefully:', err.message);
    // For DOM errors, we'll just log them and not show the error boundary
    // This allows the component to recover on the next render cycle
    return false;
  }

  error.value = err;
  errorInfo.value = {
    component: instance?.$options?.name || 'Unknown',
    props: instance?.$props || {},
    info
  };

  // Prevent the error from propagating further
  return false;
});

// Provide error handling methods to child components
provide('errorBoundary', {
  setError: (err) => {
    error.value = err;
  },
  clearError: () => {
    error.value = null;
    errorInfo.value = null;
  }
});
</script>

<style scoped>
.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
}

.error-container {
  max-width: 600px;
  padding: 2rem;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 3rem;
  color: #ff3860;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.error-message {
  margin-bottom: 1.5rem;
  color: #4a4a4a;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.error-details {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  text-align: left;
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}

.error-details pre {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.9rem;
}
</style>

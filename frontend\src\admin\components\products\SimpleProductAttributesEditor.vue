<template>
  <div class="simple-attributes-editor">
    <div class="admin-attributes-header">
      <h4 class="admin-attributes-title">
        <i class="fas fa-tags"></i>
        Product Attributes
      </h4>
    </div>

    <!-- Add New Attribute -->
    <div class="admin-add-attribute">
      <div class="admin-add-form">
        <div class="admin-field-group">
          <label class="admin-field-label">Attribute Name</label>
          <input
            v-model="newAttribute.key"
            type="text"
            class="admin-field-input"
            placeholder="e.g., Color, Size, Material"
            @keydown.enter="addValue"
          />
        </div>
        <div class="admin-field-group">
          <label class="admin-field-label">Value</label>
          <input
            v-model="newAttribute.value"
            type="text"
            class="admin-field-input"
            placeholder="Enter attribute value"
            @keydown.enter="addValue"
          />
        </div>
        <button
          type="button"
          class="admin-btn admin-btn-primary"
          @click="addValue"
          :disabled="!newAttribute.key.trim() || !newAttribute.value.trim()"
        >
          <i class="fas fa-plus"></i>
          Add
        </button>
      </div>
    </div>

    <!-- Attributes Table -->
    <div v-if="Object.keys(attributesData).length > 0" class="admin-attributes-table">
      <table class="admin-table">
        <thead>
          <tr>
            <th>Attribute Name</th>
            <th>Values</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(values, key) in attributesData"
            :key="key"
            class="admin-attribute-row"
          >
            <td class="admin-attribute-key">
              <div class="admin-key-cell">
                <i class="fas fa-grip-vertical admin-drag-handle"></i>
                <span class="admin-key-name">{{ key }}</span>
              </div>
            </td>
            <td class="admin-attribute-values">
              <div class="admin-values-list">
                <span
                  v-for="(value, valueIndex) in (Array.isArray(values) ? values : [values])"
                  :key="valueIndex"
                  class="admin-value-tag"
                >
                  {{ value }}
                  <button
                    type="button"
                    class="admin-value-remove"
                    @click="removeValue(key, valueIndex)"
                    title="Remove this value"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
            </td>
            <td class="admin-attribute-actions">
              <div class="admin-action-buttons">
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-secondary"
                  @click="moveAttributeUp(key)"
                  :disabled="isFirstAttribute(key)"
                  title="Move up"
                >
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-secondary"
                  @click="moveAttributeDown(key)"
                  :disabled="isLastAttribute(key)"
                  title="Move down"
                >
                  <i class="fas fa-arrow-down"></i>
                </button>
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-danger"
                  @click="removeAttribute(key)"
                  title="Remove entire attribute"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-else class="admin-empty-state">
      <i class="fas fa-tags admin-empty-icon"></i>
      <p class="admin-empty-text">No attributes added yet</p>
      <p class="admin-empty-subtext">Add attributes to describe your product features</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// State
const newAttribute = ref({
  key: '',
  value: ''
});

const attributesData = ref(processAttributes(props.modelValue));

// Computed
const attributeKeys = computed(() => Object.keys(attributesData.value));

// Helper function to process attributes from different formats
const processAttributes = (attributes) => {
  if (!attributes) return {};

  // If it's already an object with arrays as values, return as is
  if (typeof attributes === 'object' && !Array.isArray(attributes)) {
    const processed = {};
    Object.keys(attributes).forEach(key => {
      const value = attributes[key];
      if (Array.isArray(value)) {
        processed[key] = value;
      } else if (typeof value === 'string') {
        // Split by comma or treat as single value
        processed[key] = value.includes(',') ? value.split(',').map(v => v.trim()) : [value];
      } else {
        processed[key] = [String(value)];
      }
    });
    return processed;
  }

  // If it's a string (JSON), parse it
  if (typeof attributes === 'string') {
    try {
      const parsed = JSON.parse(attributes);
      return processAttributes(parsed);
    } catch (e) {
      console.error('Error parsing attributes:', e);
      return {};
    }
  }

  return {};
};

// Methods
const addValue = () => {
  const key = newAttribute.value.key.trim();
  const value = newAttribute.value.value.trim();

  if (!key || !value) return;

  if (attributesData.value[key]) {
    // Ensure existing attribute is an array
    if (!Array.isArray(attributesData.value[key])) {
      attributesData.value[key] = [attributesData.value[key]];
    }
    // Add to existing attribute if not already present
    if (!attributesData.value[key].includes(value)) {
      attributesData.value[key].push(value);
    }
  } else {
    // Create new attribute
    attributesData.value[key] = [value];
  }

  // Clear form
  newAttribute.value.key = '';
  newAttribute.value.value = '';

  updateModelValue();
};

const removeValue = (key, valueIndex) => {
  // Ensure the value is an array
  if (!Array.isArray(attributesData.value[key])) {
    attributesData.value[key] = [attributesData.value[key]];
  }

  attributesData.value[key].splice(valueIndex, 1);

  // Remove attribute if no values left
  if (attributesData.value[key].length === 0) {
    delete attributesData.value[key];
  }

  updateModelValue();
};

const removeAttribute = (key) => {
  delete attributesData.value[key];
  updateModelValue();
};

const moveAttributeUp = (key) => {
  const keys = attributeKeys.value;
  const currentIndex = keys.indexOf(key);
  
  if (currentIndex > 0) {
    const newData = {};
    keys.forEach((k, index) => {
      if (index === currentIndex - 1) {
        newData[key] = attributesData.value[key];
      } else if (index === currentIndex) {
        newData[keys[currentIndex - 1]] = attributesData.value[keys[currentIndex - 1]];
      } else {
        newData[k] = attributesData.value[k];
      }
    });
    
    attributesData.value = newData;
    updateModelValue();
  }
};

const moveAttributeDown = (key) => {
  const keys = attributeKeys.value;
  const currentIndex = keys.indexOf(key);
  
  if (currentIndex < keys.length - 1) {
    const newData = {};
    keys.forEach((k, index) => {
      if (index === currentIndex) {
        newData[keys[currentIndex + 1]] = attributesData.value[keys[currentIndex + 1]];
      } else if (index === currentIndex + 1) {
        newData[key] = attributesData.value[key];
      } else {
        newData[k] = attributesData.value[k];
      }
    });
    
    attributesData.value = newData;
    updateModelValue();
  }
};

const isFirstAttribute = (key) => {
  return attributeKeys.value.indexOf(key) === 0;
};

const isLastAttribute = (key) => {
  return attributeKeys.value.indexOf(key) === attributeKeys.value.length - 1;
};

const updateModelValue = () => {
  emit('update:modelValue', { ...attributesData.value });
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  attributesData.value = processAttributes(newValue);
}, { deep: true, immediate: true });
</script>

<style scoped>
.simple-attributes-editor {
  width: 100%;
}

.admin-attributes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-md);
}

.admin-attributes-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-add-attribute {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  margin-bottom: var(--admin-space-md);
}

.admin-add-form {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--admin-space-md);
  align-items: end;
}

.admin-field-group {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-field-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
}

.admin-field-input {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-field-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: none;
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-base);
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover:not(:disabled) {
  background: var(--admin-primary-dark);
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  border: 1px solid var(--admin-border-light);
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-xs {
  padding: 4px 8px;
  font-size: var(--admin-text-xs);
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-attributes-table {
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background: var(--admin-bg-secondary);
  padding: var(--admin-space-sm);
  text-align: left;
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-table td {
  padding: var(--admin-space-sm);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: top;
}

.admin-attribute-row:last-child td {
  border-bottom: none;
}

.admin-key-cell {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-drag-handle {
  color: var(--admin-text-secondary);
  cursor: grab;
}

.admin-key-name {
  font-weight: var(--admin-font-medium);
}

.admin-values-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-xs);
}

.admin-value-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: 4px 8px;
  background: var(--admin-primary);
  color: white;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
}

.admin-value-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: var(--admin-text-xs);
  opacity: 0.8;
}

.admin-value-remove:hover {
  opacity: 1;
}

.admin-action-buttons {
  display: flex;
  gap: var(--admin-space-xs);
}

.admin-empty-state {
  text-align: center;
  padding: var(--admin-space-xl);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-xs);
}

.admin-empty-subtext {
  font-size: var(--admin-text-sm);
}

@media (max-width: 768px) {
  .admin-add-form {
    grid-template-columns: 1fr;
  }
  
  .admin-table {
    font-size: var(--admin-text-sm);
  }
  
  .admin-action-buttons {
    flex-direction: column;
  }
}
</style>

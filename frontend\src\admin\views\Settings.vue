<template>
  <div class="admin-settings">
    <h1 class="title">Settings</h1>

    <!-- Loading indicator -->
    <div v-if="loading" class="has-text-centered" style="padding: 2rem;">
      <div class="is-size-4">
        <i class="fas fa-spinner fa-spin"></i>
        Loading settings...
      </div>
    </div>

    <div v-else>
      <div class="tabs">
        <ul>
          <li :class="{ 'is-active': activeTab === 'general' }">
            <a @click="activeTab = 'general'">General</a>
          </li>
          <li :class="{ 'is-active': activeTab === 'payment' }">
            <a @click="activeTab = 'payment'">Payment</a>
          </li>
          <li :class="{ 'is-active': activeTab === 'shipping' }">
            <a @click="activeTab = 'shipping'">Shipping</a>
          </li>
          <li :class="{ 'is-active': activeTab === 'email' }">
            <a @click="activeTab = 'email'">Email</a>
          </li>
          <li :class="{ 'is-active': activeTab === 'api' }">
            <a @click="activeTab = 'api'">API</a>
          </li>
        </ul>
      </div>
    
    <!-- General Settings -->
    <div v-if="activeTab === 'general'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">General Settings</h2>
        
        <div class="field">
          <label class="label">Site Name</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.general.siteName">
          </div>
        </div>
        
        <div class="field">
          <label class="label">Site Description</label>
          <div class="control">
            <textarea class="textarea" v-model="settings.general.siteDescription"></textarea>
          </div>
        </div>
        
        <div class="field">
          <label class="label">Currency</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="settings.general.currency">
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
                <option value="JPY">Japanese Yen (JPY)</option>
                <option value="CAD">Canadian Dollar (CAD)</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="field">
          <label class="label">Default Language</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="settings.general.language">
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="ja">Japanese</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.general.maintenanceMode">
              Enable Maintenance Mode
            </label>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Payment Settings -->
    <div v-if="activeTab === 'payment'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Payment Settings</h2>
        
        <div class="field">
          <label class="label">Payment Methods</label>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.payment.methods.creditCard">
              Credit Card
            </label>
          </div>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.payment.methods.paypal">
              PayPal
            </label>
          </div>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.payment.methods.bankTransfer">
              Bank Transfer
            </label>
          </div>
        </div>
        
        <div class="field">
          <label class="label">Stripe API Key</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.payment.stripeApiKey">
          </div>
        </div>
        
        <div class="field">
          <label class="label">PayPal Client ID</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.payment.paypalClientId">
          </div>
        </div>
        
        <div class="field">
          <label class="label">Commission Rate (%)</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.payment.commissionRate">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Shipping Settings -->
    <div v-if="activeTab === 'shipping'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Shipping Settings</h2>
        
        <div class="field">
          <label class="label">Shipping Methods</label>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.shipping.methods.standard">
              Standard Shipping
            </label>
          </div>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.shipping.methods.express">
              Express Shipping
            </label>
          </div>
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.shipping.methods.freeShipping">
              Free Shipping
            </label>
          </div>
        </div>
        
        <div class="field">
          <label class="label">Free Shipping Minimum Order Amount</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.freeShippingMinimum">
          </div>
        </div>
        
        <div class="field">
          <label class="label">Standard Shipping Rate</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.standardRate">
          </div>
        </div>
        
        <div class="field">
          <label class="label">Express Shipping Rate</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.expressRate">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Email Settings -->
    <div v-if="activeTab === 'email'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Email Settings</h2>
        
        <div class="field">
          <label class="label">SMTP Server</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.smtpServer">
          </div>
        </div>
        
        <div class="field">
          <label class="label">SMTP Port</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.email.smtpPort">
          </div>
        </div>
        
        <div class="field">
          <label class="label">SMTP Username</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.smtpUsername">
          </div>
        </div>
        
        <div class="field">
          <label class="label">SMTP Password</label>
          <div class="control">
            <input class="input" type="password" v-model="settings.email.smtpPassword">
          </div>
        </div>
        
        <div class="field">
          <label class="label">From Email</label>
          <div class="control">
            <input class="input" type="email" v-model="settings.email.fromEmail">
          </div>
        </div>
        
        <div class="field">
          <label class="label">From Name</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.fromName">
          </div>
        </div>
      </div>
    </div>
    
    <!-- API Settings -->
    <div v-if="activeTab === 'api'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">API Settings</h2>
        
        <div class="field">
          <label class="label">API Key</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.api.apiKey" readonly>
          </div>
          <p class="help">This is your API key. Keep it secure.</p>
        </div>
        
        <div class="field">
          <div class="control">
            <button class="button is-info" @click="regenerateApiKey">
              Regenerate API Key
            </button>
          </div>
        </div>
        
        <div class="field">
          <label class="label">API Rate Limit (requests per minute)</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.api.rateLimit">
          </div>
        </div>
        
        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="settings.api.enableWebhooks">
              Enable Webhooks
            </label>
          </div>
        </div>
        
        <div class="field" v-if="settings.api.enableWebhooks">
          <label class="label">Webhook URL</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.api.webhookUrl">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Save Button -->
    <div class="field is-grouped is-grouped-right mt-4">
      <div class="control" v-if="activeTab === 'email'">
        <button class="button is-info" @click="testEmailSettings" :disabled="loading || saving">
          Test Email Settings
        </button>
      </div>
      <div class="control">
        <button
          class="button is-primary"
          @click="saveSettings"
          :class="{ 'is-loading': saving }"
          :disabled="loading || saving"
        >
          Save Settings
        </button>
      </div>
    </div>
    </div> <!-- Close v-else div -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { settingsService } from '@/admin/services/settings';

// Active tab
const activeTab = ref('general');

// Loading states
const loading = ref(false);
const saving = ref(false);

// Settings data
const settings = ref({
  general: {},
  payment: {},
  shipping: {},
  email: {},
  system: {}
});

// Methods
const loadSettings = async () => {
  loading.value = true;
  try {
    const [general, payment, shipping, email, system] = await Promise.all([
      settingsService.getGeneralSettings(),
      settingsService.getPaymentSettings(),
      settingsService.getShippingSettings(),
      settingsService.getEmailSettings(),
      settingsService.getSystemInfo()
    ]);

    settings.value = {
      general: general.data || general,
      payment: payment.data || payment,
      shipping: shipping.data || shipping,
      email: email.data || email,
      system: system.data || system
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    alert('Error loading settings. Please try again.');
  } finally {
    loading.value = false;
  }
};

const saveSettings = async () => {
  saving.value = true;
  try {
    const currentTab = activeTab.value;
    let result;

    switch (currentTab) {
      case 'general':
        result = await settingsService.updateGeneralSettings(settings.value.general);
        break;
      case 'payment':
        result = await settingsService.updatePaymentSettings(settings.value.payment);
        break;
      case 'shipping':
        result = await settingsService.updateShippingSettings(settings.value.shipping);
        break;
      case 'email':
        result = await settingsService.updateEmailSettings(settings.value.email);
        break;
      default:
        throw new Error('Unknown settings tab');
    }

    if (result.success !== false) {
      alert('Settings saved successfully!');
    } else {
      throw new Error('Failed to save settings');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    alert('Error saving settings. Please try again.');
  } finally {
    saving.value = false;
  }
};

const testEmailSettings = async () => {
  try {
    await settingsService.testEmailSettings(settings.value.email);
    alert('Test email sent successfully!');
  } catch (error) {
    console.error('Error testing email settings:', error);
    alert('Failed to send test email. Please check your settings.');
  }
};

// Initialize
onMounted(() => {
  loadSettings();
});
</script>

<style scoped>
.admin-settings {
  padding: 1rem;
}

.tabs a {
  color: #9ca3af;
}

.tabs li.is-active a {
  color: #f3f4f6;
  border-bottom-color: #3B82F6;
}

.settings-content {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}
</style>

﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.Category;

public class UpdateCategoryCommandHandler : IRequestHandler<UpdateCategoryCommand, Unit>
{
    private readonly ICategoryRepository _repository;
    private readonly IMapper _mapper;

    public UpdateCategoryCommandHandler(ICategoryRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Unit> Handle(UpdateCategoryCommand command, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
        if (item == null)
            throw new InvalidOperationException($"Категорію з Ід {command.Id} не знайдено.");

        // Викликаємо метод Update із доменної моделі
        item.Update(
            name: command.Name,
            slug: command.Slug != null ? new Slug(command.Slug) : null,
            description: command.Description,
            image: command.Image != null ? new Url(command.Image) : null,
            parentId: command.ParentId,
            metaTitle: command.MetaTitle,
            metaDescription: command.MetaDescription,
            metaImage: command.MetaImage != null ? new Url(command.MetaImage) : null
        );

        await _repository.UpdateAsync(item, cancellationToken);
        return Unit.Value;
    }
}


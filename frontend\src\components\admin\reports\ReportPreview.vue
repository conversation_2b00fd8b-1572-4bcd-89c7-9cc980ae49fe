<template>
  <div class="report-preview">
    <!-- Report Header -->
    <div class="report-header">
      <div class="report-title-section">
        <h2 class="report-title">
          <i :class="reportIcon"></i>
          {{ reportTitle }}
        </h2>
        <div class="report-period">
          {{ formatDateRange(dateRange.startDate, dateRange.endDate) }}
        </div>
      </div>
      <div class="report-actions">
        <button @click="refreshReport" class="refresh-btn" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Key Metrics Summary -->
    <div class="metrics-summary">
      <div class="metrics-grid">
        <div 
          v-for="metric in keyMetrics" 
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-icon">
            <i :class="metric.icon"></i>
          </div>
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ metric.value }}</div>
            <div v-if="metric.change" class="metric-change">
              <i :class="metric.changeIcon"></i>
              {{ metric.change }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- Primary Chart -->
        <div class="chart-container primary-chart">
          <div class="chart-header">
            <h3 class="chart-title">{{ primaryChart.title }}</h3>
            <div class="chart-controls">
              <select v-model="primaryChart.type" @change="updatePrimaryChart" class="chart-type-select">
                <option value="line">Line Chart</option>
                <option value="bar">Bar Chart</option>
                <option value="area">Area Chart</option>
              </select>
            </div>
          </div>
          <div class="chart-content">
            <canvas ref="primaryChartCanvas" :id="primaryChart.id"></canvas>
          </div>
        </div>

        <!-- Secondary Chart -->
        <div class="chart-container secondary-chart">
          <div class="chart-header">
            <h3 class="chart-title">{{ secondaryChart.title }}</h3>
          </div>
          <div class="chart-content">
            <canvas ref="secondaryChartCanvas" :id="secondaryChart.id"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="data-table-section">
      <div class="table-header">
        <h3 class="table-title">{{ tableTitle }}</h3>
        <div class="table-controls">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="Search..."
              class="search-input"
            />
          </div>
          <select v-model="sortBy" @change="sortTable" class="sort-select">
            <option value="">Sort by...</option>
            <option v-for="column in sortableColumns" :key="column.key" :value="column.key">
              {{ column.label }}
            </option>
          </select>
        </div>
      </div>
      
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th 
                v-for="column in tableColumns" 
                :key="column.key"
                @click="handleSort(column.key)"
                :class="{ sortable: column.sortable, active: sortBy === column.key }"
              >
                {{ column.label }}
                <i v-if="column.sortable" class="fas fa-sort sort-icon"></i>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="row in paginatedData" :key="row.id" class="table-row">
              <td v-for="column in tableColumns" :key="column.key" :class="column.class">
                <span v-if="column.type === 'currency'">
                  {{ formatCurrency(row[column.key]) }}
                </span>
                <span v-else-if="column.type === 'percentage'">
                  {{ formatPercentage(row[column.key]) }}
                </span>
                <span v-else-if="column.type === 'number'">
                  {{ formatNumber(row[column.key]) }}
                </span>
                <span v-else-if="column.type === 'date'">
                  {{ formatDate(row[column.key]) }}
                </span>
                <span v-else-if="column.type === 'status'" :class="`status-${row[column.key]?.toLowerCase()}`">
                  {{ row[column.key] }}
                </span>
                <span v-else>
                  {{ row[column.key] }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination">
        <button 
          @click="currentPage = 1" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button 
          @click="currentPage--" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-left"></i>
        </button>
        
        <span class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }} ({{ filteredData.length }} items)
        </span>
        
        <button 
          @click="currentPage++" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button 
          @click="currentPage = totalPages" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    </div>

    <!-- Insights Section -->
    <div v-if="insights.length > 0" class="insights-section">
      <h3 class="insights-title">
        <i class="fas fa-lightbulb"></i>
        Key Insights
      </h3>
      <div class="insights-list">
        <div 
          v-for="insight in insights" 
          :key="insight.id"
          class="insight-item"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

// Chart.js will be loaded dynamically when needed
let Chart = null
let chartRegistered = false

const loadChart = async () => {
  if (!Chart) {
    const chartModule = await import('chart.js')
    Chart = chartModule.Chart
    if (!chartRegistered) {
      Chart.register(...chartModule.registerables)
      chartRegistered = true
    }
  }
  return Chart
}

export default {
  name: 'ReportPreview',
  props: {
    reportType: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    // Refs
    const primaryChartCanvas = ref(null)
    const secondaryChartCanvas = ref(null)
    const loading = ref(false)
    const searchQuery = ref('')
    const sortBy = ref('')
    const sortDirection = ref('asc')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)

    // Chart instances
    let primaryChartInstance = null
    let secondaryChartInstance = null

    // Computed properties
    const reportTitle = computed(() => {
      const titles = {
        financial: 'Financial Report',
        sales: 'Sales Report',
        products: 'Product Report',
        users: 'User Report',
        orders: 'Order Report'
      }
      return titles[props.reportType] || 'Report'
    })

    const reportIcon = computed(() => {
      const icons = {
        financial: 'fas fa-dollar-sign',
        sales: 'fas fa-chart-line',
        products: 'fas fa-box',
        users: 'fas fa-users',
        orders: 'fas fa-shopping-cart'
      }
      return icons[props.reportType] || 'fas fa-chart-bar'
    })

    const keyMetrics = computed(() => {
      if (!props.data?.metrics) return []
      
      return props.data.metrics.map(metric => ({
        ...metric,
        value: formatMetricValue(metric.value, metric.type),
        change: metric.previousValue ? 
          formatPercentageChange(metric.value, metric.previousValue) : null,
        changeIcon: getChangeIcon(metric.value, metric.previousValue),
        trend: getTrend(metric.value, metric.previousValue)
      }))
    })

    const primaryChart = computed(() => ({
      id: 'primary-chart',
      title: props.data?.charts?.primary?.title || 'Primary Chart',
      type: ref('line'),
      data: props.data?.charts?.primary?.data || []
    }))

    const secondaryChart = computed(() => ({
      id: 'secondary-chart',
      title: props.data?.charts?.secondary?.title || 'Secondary Chart',
      data: props.data?.charts?.secondary?.data || []
    }))

    const tableTitle = computed(() => {
      return props.data?.table?.title || 'Data Table'
    })

    const tableColumns = computed(() => {
      return props.data?.table?.columns || []
    })

    const sortableColumns = computed(() => {
      return tableColumns.value.filter(col => col.sortable)
    })

    const filteredData = computed(() => {
      let data = props.data?.table?.data || []
      
      // Apply search filter
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        data = data.filter(row => 
          Object.values(row).some(value => 
            String(value).toLowerCase().includes(query)
          )
        )
      }
      
      // Apply sorting
      if (sortBy.value) {
        data = [...data].sort((a, b) => {
          const aVal = a[sortBy.value]
          const bVal = b[sortBy.value]
          
          if (typeof aVal === 'number' && typeof bVal === 'number') {
            return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
          }
          
          const aStr = String(aVal).toLowerCase()
          const bStr = String(bVal).toLowerCase()
          
          if (sortDirection.value === 'asc') {
            return aStr.localeCompare(bStr)
          } else {
            return bStr.localeCompare(aStr)
          }
        })
      }
      
      return data
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredData.value.length / itemsPerPage.value)
    })

    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredData.value.slice(start, end)
    })

    const insights = computed(() => {
      return props.data?.insights || []
    })

    // Methods
    const formatDateRange = (startDate, endDate) => {
      if (!startDate || !endDate) return ''
      
      const start = new Date(startDate).toLocaleDateString('uk-UA')
      const end = new Date(endDate).toLocaleDateString('uk-UA')
      
      return `${start} - ${end}`
    }

    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatPercentage = (value) => {
      return reportsService.formatPercentage(value)
    }

    const formatNumber = (value) => {
      return reportsService.formatNumber(value)
    }

    const formatDate = (value) => {
      return new Date(value).toLocaleDateString('uk-UA')
    }

    const formatPercentageChange = (current, previous) => {
      const change = reportsService.calculatePercentageChange(current, previous)
      return reportsService.formatPercentage(Math.abs(change))
    }

    const getChangeIcon = (current, previous) => {
      if (!previous) return ''
      
      const change = reportsService.calculatePercentageChange(current, previous)
      
      if (change > 0) return 'fas fa-arrow-up'
      if (change < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const getTrend = (current, previous) => {
      if (!previous) return ''
      
      const change = reportsService.calculatePercentageChange(current, previous)
      
      if (change > 0) return 'positive'
      if (change < 0) return 'negative'
      return 'neutral'
    }

    const handleSort = (columnKey) => {
      if (sortBy.value === columnKey) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortBy.value = columnKey
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const sortTable = () => {
      currentPage.value = 1
    }

    const refreshReport = () => {
      loading.value = true
      emit('refresh')
      setTimeout(() => {
        loading.value = false
      }, 1000)
    }

    const createPrimaryChart = async () => {
      await nextTick()

      if (primaryChartInstance) {
        primaryChartInstance.destroy()
      }

      if (!primaryChartCanvas.value || !primaryChart.value.data) return

      const ChartClass = await loadChart()
      const ctx = primaryChartCanvas.value.getContext('2d')

      primaryChartInstance = new ChartClass(ctx, {
        type: primaryChart.value.type.value,
        data: primaryChart.value.data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      })
    }

    const createSecondaryChart = async () => {
      await nextTick()

      if (secondaryChartInstance) {
        secondaryChartInstance.destroy()
      }

      if (!secondaryChartCanvas.value || !secondaryChart.value.data) return

      const ChartClass = await loadChart()
      const ctx = secondaryChartCanvas.value.getContext('2d')

      secondaryChartInstance = new ChartClass(ctx, {
        type: 'doughnut',
        data: secondaryChart.value.data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      })
    }

    const updatePrimaryChart = () => {
      createPrimaryChart()
    }

    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createPrimaryChart()
        createSecondaryChart()
      })
    }, { deep: true })

    watch(searchQuery, () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createPrimaryChart()
        createSecondaryChart()
      })
    })

    onUnmounted(() => {
      if (primaryChartInstance) {
        primaryChartInstance.destroy()
      }
      if (secondaryChartInstance) {
        secondaryChartInstance.destroy()
      }
    })

    return {
      primaryChartCanvas,
      secondaryChartCanvas,
      loading,
      searchQuery,
      sortBy,
      currentPage,
      itemsPerPage,
      reportTitle,
      reportIcon,
      keyMetrics,
      primaryChart,
      secondaryChart,
      tableTitle,
      tableColumns,
      sortableColumns,
      filteredData,
      totalPages,
      paginatedData,
      insights,
      formatDateRange,
      formatCurrency,
      formatPercentage,
      formatNumber,
      formatDate,
      handleSort,
      sortTable,
      refreshReport,
      updatePrimaryChart
    }
  }
}
</script>

<style scoped>
.report-preview {
  padding: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.report-title-section {
  flex: 1;
}

.report-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.report-period {
  color: #6b7280;
  font-size: 1rem;
}

.report-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.metrics-summary {
  margin-bottom: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6b7280;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.positive .metric-change {
  color: #10b981;
}

.negative .metric-change {
  color: #ef4444;
}

.neutral .metric-change {
  color: #6b7280;
}

.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.chart-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-type-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

.chart-content {
  height: 300px;
  position: relative;
}

.data-table-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.table-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #6b7280;
}

.search-input {
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 200px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f9fafb;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  opacity: 0.5;
}

.data-table th.active .sort-icon {
  opacity: 1;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.table-row:hover {
  background: #f9fafb;
}

.status-pending {
  color: #f59e0b;
}

.status-processing {
  color: #3b82f6;
}

.status-completed,
.status-delivered {
  color: #10b981;
}

.status-cancelled {
  color: #ef4444;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.pagination-btn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

.insights-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.insights-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
}

.insight-item.positive {
  background: #f0fdf4;
  border-left-color: #10b981;
}

.insight-item.negative {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.insight-item.warning {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.insight-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.insight-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
}
</style>

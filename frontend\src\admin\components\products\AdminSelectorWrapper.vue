<template>
  <div class="admin-selector-wrapper">
    <Suspense>
      <template #default>
        <div v-if="isReady" :key="componentKey">
          <slot />
        </div>
        <div v-else class="admin-selector-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Loading...</span>
        </div>
      </template>
      <template #fallback>
        <div class="admin-selector-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Loading...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';

// Props
const props = defineProps({
  delay: {
    type: Number,
    default: 100
  }
});

// State
const isReady = ref(false);
const componentKey = ref(0);
const isMounted = ref(false);

// Lifecycle
onMounted(async () => {
  try {
    isMounted.value = true;
    
    // Add a small delay to ensure DOM is stable
    await new Promise(resolve => setTimeout(resolve, props.delay));
    
    if (isMounted.value) {
      await nextTick();
      isReady.value = true;
    }
  } catch (error) {
    console.error('Error in AdminSelectorWrapper onMounted:', error);
  }
});

onBeforeUnmount(() => {
  isMounted.value = false;
  isReady.value = false;
  componentKey.value += 1; // Force re-render on next mount
});

// Provide methods to child components
defineExpose({
  refresh: () => {
    if (isMounted.value) {
      componentKey.value += 1;
    }
  },
  isReady: () => isReady.value && isMounted.value
});
</script>

<style scoped>
.admin-selector-wrapper {
  width: 100%;
}

.admin-selector-loading {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-sm);
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
}

.admin-selector-loading i {
  color: var(--admin-primary);
}
</style>

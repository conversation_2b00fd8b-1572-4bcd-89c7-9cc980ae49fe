<template>
  <section class="recommended-products-section">
    <h2 class="section-title">Рекомендації на основі ваших переглядів</h2>\
    <div class="products-grid">
      <div v-for="product in products" :key="product.id" class="product-card">
        <div class="product-badge" v-if="product.badge">{{ product.badge }}</div>
        <div class="product-image">
          <img :src="product.image" :alt="product.name" />
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-availability" v-if="product.stock > 0">
            <span class="availability-icon">✓</span>
            <span class="availability-text">В наявності</span>
          </div>
            <div class="product-unavailability" v-if="product.stock == 0">
              <span class="availability-icon">✖</span>
              <span class="availability-text">Немає в наявності</span>
            </div>
          <div class="product-price">
            <div class="price-old" v-if="product.oldPrice">{{ product.oldPrice }} ₴</div>
            <div class="price-discount" v-if="product.discount">-{{ product.discount }}%</div>
          </div>
          <div class="price-current">{{ Math.round(product.priceAmount) }} ₴</div>
        </div>
        <div class="product-actions">
          <button class="wishlist-btn"
                    @click="addToWishlist(product.id)">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>
          </button>
          <button class="cart-btn"
                    @click="addToCart(product.id)">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="9" cy="21" r="1"></circle>
              <circle cx="20" cy="21" r="1"></circle>
              <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import cartService from '../../services/cart.service';
import wishlistService from '../../services/wishlist.service';

export default {
  props: {
    products: {
      type: Array,
      default: () => [],
    }
  },
  methods: {
    async addToCart(productId)
    {
        await cartService.addToCart(productId);
    },
    async addToWishlist(productId)
    {
        await wishlistService.addToWishlist(productId);
    }
  }
}
</script>

<style scoped>
.recommended-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.product-card {
  position: relative;
  background: white;
  border: solid #ABAAAA 2px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff7a00;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.product-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 8px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 54px;
}

.product-availability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00a046;
}

.availability-icon {
  margin-right: 4px;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.price-old {
  font-size: 12px;
  color: #888;
  text-decoration: line-through;
  margin-right: 8px;
}

.price-discount {
  font-size: 12px;
  color: white;
  background: #ff5252;
  padding: 2px 4px;
  border-radius: 4px;
}

.price-current {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  margin-bottom: 12px;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.wishlist-btn, .cart-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.wishlist-btn:hover {
  color: #ff5252;
  border-color: #ff5252;
}

.cart-btn {
  background: #0066cc;
  color: white;
  border: none;
}

.cart-btn:hover {
  background: #0055aa;
}

.cart-btn svg, .wishlist-btn svg {
  width: 18px;
  height: 18px;
}

@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
}
</style>
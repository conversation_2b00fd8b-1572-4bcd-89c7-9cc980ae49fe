<template>
  <div class="admin-form-section" :class="sectionClasses">
    <div v-if="title || description || $slots.header" class="admin-form-section-header">
      <slot name="header">
        <h3 v-if="title" class="admin-form-section-title">{{ title }}</h3>
        <p v-if="description" class="admin-form-section-description">{{ description }}</p>
      </slot>
    </div>
    
    <div class="admin-form-section-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'bordered', 'card'].includes(value)
  },
  spacing: {
    type: String,
    default: 'default',
    validator: (value) => ['compact', 'default', 'relaxed'].includes(value)
  },
  required: {
    type: Boolean,
    default: false
  }
});

// Computed
const sectionClasses = computed(() => ({
  [`admin-form-section--${props.variant}`]: props.variant !== 'default',
  [`admin-form-section--${props.spacing}`]: props.spacing !== 'default',
  'admin-form-section--required': props.required
}));
</script>

<style scoped>
.admin-form-section {
  margin-bottom: var(--admin-space-lg);
}

.admin-form-section--compact {
  margin-bottom: var(--admin-space-md);
}

.admin-form-section--relaxed {
  margin-bottom: var(--admin-space-xl);
}

/* Header */
.admin-form-section-header {
  margin-bottom: var(--admin-space-md);
}

.admin-form-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-section--required .admin-form-section-title::after {
  content: '*';
  color: var(--admin-danger);
  font-weight: normal;
}

.admin-form-section-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
  margin: 0;
  line-height: 1.5;
}

/* Content */
.admin-form-section-content {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-form-section--compact .admin-form-section-content {
  gap: var(--admin-space-sm);
}

.admin-form-section--relaxed .admin-form-section-content {
  gap: var(--admin-space-lg);
}

/* Variants */
.admin-form-section--bordered {
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  background: var(--admin-bg-secondary);
}

.admin-form-section--card {
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  background: var(--admin-bg-primary);
  box-shadow: var(--admin-shadow-sm);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-form-section--bordered,
  .admin-form-section--card {
    padding: var(--admin-space-md);
  }
  
  .admin-form-section-content {
    gap: var(--admin-space-sm);
  }
}
</style>

<template>
  <div class="report-filters">
    <div class="filters-header">
      <h3 class="filters-title">
        <i class="fas fa-filter"></i>
        Report Filters
      </h3>
      <button @click="resetFilters" class="reset-btn">
        <i class="fas fa-undo"></i>
        Reset
      </button>
    </div>

    <div class="filters-grid">
      <!-- Report Type Filter -->
      <div class="filter-group">
        <label class="filter-label">Report Type</label>
        <select 
          v-model="localReportType" 
          @change="handleReportTypeChange"
          class="filter-select"
        >
          <option value="">Select Report Type</option>
          <option value="financial">Financial Reports</option>
          <option value="sales">Sales Reports</option>
          <option value="products">Product Reports</option>
          <option value="users">User Reports</option>
          <option value="orders">Order Reports</option>
        </select>
      </div>

      <!-- Date Range Filter -->
      <div class="filter-group">
        <label class="filter-label">Date Range</label>
        <div class="date-range-container">
          <input 
            type="date" 
            v-model="localDateRange.startDate"
            @change="handleDateChange"
            class="filter-input date-input"
            placeholder="Start Date"
          />
          <span class="date-separator">to</span>
          <input 
            type="date" 
            v-model="localDateRange.endDate"
            @change="handleDateChange"
            class="filter-input date-input"
            placeholder="End Date"
          />
        </div>
      </div>

      <!-- Quick Date Presets -->
      <div class="filter-group">
        <label class="filter-label">Quick Presets</label>
        <div class="preset-buttons">
          <button 
            v-for="preset in datePresets" 
            :key="preset.key"
            @click="applyDatePreset(preset)"
            class="preset-btn"
            :class="{ active: activePreset === preset.key }"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- Dynamic Additional Filters -->
      <template v-if="localReportType">
        <!-- Financial Report Filters -->
        <template v-if="localReportType === 'financial'">
          <div class="filter-group">
            <label class="filter-label">Transaction Type</label>
            <select v-model="localAdditionalFilters.transactionType" @change="handleFiltersChange" class="filter-select">
              <option value="">All Transactions</option>
              <option value="sale">Sales</option>
              <option value="refund">Refunds</option>
              <option value="commission">Commission</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Payment Method</label>
            <select v-model="localAdditionalFilters.paymentMethod" @change="handleFiltersChange" class="filter-select">
              <option value="">All Methods</option>
              <option value="card">Credit Card</option>
              <option value="paypal">PayPal</option>
              <option value="bank_transfer">Bank Transfer</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Amount Range</label>
            <div class="amount-range">
              <input 
                type="number" 
                v-model="localAdditionalFilters.minAmount"
                @input="handleFiltersChange"
                placeholder="Min Amount"
                class="filter-input amount-input"
              />
              <span class="amount-separator">-</span>
              <input 
                type="number" 
                v-model="localAdditionalFilters.maxAmount"
                @input="handleFiltersChange"
                placeholder="Max Amount"
                class="filter-input amount-input"
              />
            </div>
          </div>
        </template>

        <!-- Sales Report Filters -->
        <template v-if="localReportType === 'sales'">
          <div class="filter-group">
            <label class="filter-label">Category</label>
            <select v-model="localAdditionalFilters.categoryId" @change="handleFiltersChange" class="filter-select">
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Seller</label>
            <select v-model="localAdditionalFilters.sellerId" @change="handleFiltersChange" class="filter-select">
              <option value="">All Sellers</option>
              <option v-for="seller in sellers" :key="seller.id" :value="seller.id">
                {{ seller.name }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Region</label>
            <select v-model="localAdditionalFilters.region" @change="handleFiltersChange" class="filter-select">
              <option value="">All Regions</option>
              <option value="kyiv">Kyiv</option>
              <option value="lviv">Lviv</option>
              <option value="odesa">Odesa</option>
              <option value="kharkiv">Kharkiv</option>
              <option value="dnipro">Dnipro</option>
            </select>
          </div>
        </template>

        <!-- Product Report Filters -->
        <template v-if="localReportType === 'products'">
          <div class="filter-group">
            <label class="filter-label">Category</label>
            <select v-model="localAdditionalFilters.categoryId" @change="handleFiltersChange" class="filter-select">
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Price Range</label>
            <div class="amount-range">
              <input 
                type="number" 
                v-model="localAdditionalFilters.minPrice"
                @input="handleFiltersChange"
                placeholder="Min Price"
                class="filter-input amount-input"
              />
              <span class="amount-separator">-</span>
              <input 
                type="number" 
                v-model="localAdditionalFilters.maxPrice"
                @input="handleFiltersChange"
                placeholder="Max Price"
                class="filter-input amount-input"
              />
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Rating Range</label>
            <div class="rating-range">
              <select v-model="localAdditionalFilters.minRating" @change="handleFiltersChange" class="filter-select">
                <option value="">Min Rating</option>
                <option value="1">1 Star</option>
                <option value="2">2 Stars</option>
                <option value="3">3 Stars</option>
                <option value="4">4 Stars</option>
                <option value="5">5 Stars</option>
              </select>
              <select v-model="localAdditionalFilters.maxRating" @change="handleFiltersChange" class="filter-select">
                <option value="">Max Rating</option>
                <option value="1">1 Star</option>
                <option value="2">2 Stars</option>
                <option value="3">3 Stars</option>
                <option value="4">4 Stars</option>
                <option value="5">5 Stars</option>
              </select>
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Product Status</label>
            <select v-model="localAdditionalFilters.productStatus" @change="handleFiltersChange" class="filter-select">
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="sale">On Sale</option>
            </select>
          </div>
        </template>

        <!-- User Report Filters -->
        <template v-if="localReportType === 'users'">
          <div class="filter-group">
            <label class="filter-label">User Role</label>
            <select v-model="localAdditionalFilters.userRole" @change="handleFiltersChange" class="filter-select">
              <option value="">All Roles</option>
              <option value="buyer">Buyers</option>
              <option value="seller">Sellers</option>
              <option value="seller_owner">Seller Owners</option>
              <option value="admin">Admins</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Activity Status</label>
            <select v-model="localAdditionalFilters.activityStatus" @change="handleFiltersChange" class="filter-select">
              <option value="">All Users</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Region</label>
            <select v-model="localAdditionalFilters.region" @change="handleFiltersChange" class="filter-select">
              <option value="">All Regions</option>
              <option value="kyiv">Kyiv</option>
              <option value="lviv">Lviv</option>
              <option value="odesa">Odesa</option>
              <option value="kharkiv">Kharkiv</option>
              <option value="dnipro">Dnipro</option>
            </select>
          </div>
        </template>

        <!-- Order Report Filters -->
        <template v-if="localReportType === 'orders'">
          <div class="filter-group">
            <label class="filter-label">Order Status</label>
            <select v-model="localAdditionalFilters.orderStatus" @change="handleFiltersChange" class="filter-select">
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Payment Method</label>
            <select v-model="localAdditionalFilters.paymentMethod" @change="handleFiltersChange" class="filter-select">
              <option value="">All Methods</option>
              <option value="card">Credit Card</option>
              <option value="paypal">PayPal</option>
              <option value="bank_transfer">Bank Transfer</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Shipping Method</label>
            <select v-model="localAdditionalFilters.shippingMethod" @change="handleFiltersChange" class="filter-select">
              <option value="">All Methods</option>
              <option value="standard">Standard</option>
              <option value="express">Express</option>
              <option value="overnight">Overnight</option>
            </select>
          </div>
        </template>
      </template>
    </div>

    <!-- Apply Filters Button -->
    <div class="filters-actions">
      <button @click="applyFilters" class="apply-btn" :disabled="!canApplyFilters">
        <i class="fas fa-search"></i>
        Generate Report
      </button>
      <div class="filters-summary" v-if="activeFiltersCount > 0">
        {{ activeFiltersCount }} filter{{ activeFiltersCount > 1 ? 's' : '' }} applied
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'

export default {
  name: 'ReportFilters',
  props: {
    reportType: {
      type: String,
      default: ''
    },
    dateRange: {
      type: Object,
      default: () => ({
        startDate: null,
        endDate: null
      })
    },
    additionalFilters: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:reportType', 'update:dateRange', 'update:additionalFilters', 'filtersChanged'],
  setup(props, { emit }) {
    // Local reactive state
    const localReportType = ref(props.reportType)
    const localDateRange = reactive({
      startDate: props.dateRange.startDate ? formatDateForInput(props.dateRange.startDate) : '',
      endDate: props.dateRange.endDate ? formatDateForInput(props.dateRange.endDate) : ''
    })
    const localAdditionalFilters = reactive({ ...props.additionalFilters })
    const activePreset = ref('')

    // Data
    const categories = ref([])
    const sellers = ref([])

    const datePresets = [
      { key: 'today', label: 'Today', days: 0 },
      { key: 'yesterday', label: 'Yesterday', days: 1 },
      { key: 'last7days', label: 'Last 7 Days', days: 7 },
      { key: 'last30days', label: 'Last 30 Days', days: 30 },
      { key: 'last90days', label: 'Last 90 Days', days: 90 },
      { key: 'thisMonth', label: 'This Month', type: 'month' },
      { key: 'lastMonth', label: 'Last Month', type: 'lastMonth' }
    ]

    // Computed
    const canApplyFilters = computed(() => {
      return localReportType.value && localDateRange.startDate && localDateRange.endDate
    })

    const activeFiltersCount = computed(() => {
      let count = 0
      if (localReportType.value) count++
      if (localDateRange.startDate && localDateRange.endDate) count++
      
      Object.values(localAdditionalFilters).forEach(value => {
        if (value !== '' && value !== null && value !== undefined) count++
      })
      
      return count
    })

    // Methods
    function formatDateForInput(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toISOString().split('T')[0]
    }

    function handleReportTypeChange() {
      // Clear additional filters when report type changes
      Object.keys(localAdditionalFilters).forEach(key => {
        delete localAdditionalFilters[key]
      })
      
      emit('update:reportType', localReportType.value)
      emit('filtersChanged')
    }

    function handleDateChange() {
      activePreset.value = ''
      const startDate = localDateRange.startDate ? new Date(localDateRange.startDate) : null
      const endDate = localDateRange.endDate ? new Date(localDateRange.endDate) : null
      
      emit('update:dateRange', { startDate, endDate })
      emit('filtersChanged')
    }

    function handleFiltersChange() {
      emit('update:additionalFilters', { ...localAdditionalFilters })
      emit('filtersChanged')
    }

    function applyDatePreset(preset) {
      activePreset.value = preset.key
      const now = new Date()
      let startDate, endDate

      if (preset.type === 'month') {
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      } else if (preset.type === 'lastMonth') {
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        endDate = new Date(now.getFullYear(), now.getMonth(), 0)
      } else {
        startDate = new Date(now.getTime() - preset.days * 24 * 60 * 60 * 1000)
        endDate = now
      }

      localDateRange.startDate = formatDateForInput(startDate)
      localDateRange.endDate = formatDateForInput(endDate)
      
      handleDateChange()
    }

    function resetFilters() {
      localReportType.value = ''
      localDateRange.startDate = ''
      localDateRange.endDate = ''
      Object.keys(localAdditionalFilters).forEach(key => {
        delete localAdditionalFilters[key]
      })
      activePreset.value = ''
      
      emit('update:reportType', '')
      emit('update:dateRange', { startDate: null, endDate: null })
      emit('update:additionalFilters', {})
      emit('filtersChanged')
    }

    function applyFilters() {
      if (canApplyFilters.value) {
        emit('filtersChanged')
      }
    }

    // Load data for dropdowns
    async function loadFilterData() {
      try {
        // Load categories and sellers for filter dropdowns
        // This would typically come from your API services
        categories.value = [
          { id: 1, name: 'Electronics' },
          { id: 2, name: 'Clothing' },
          { id: 3, name: 'Books' },
          { id: 4, name: 'Home & Garden' },
          { id: 5, name: 'Sports' }
        ]

        sellers.value = [
          { id: 1, name: 'TechStore' },
          { id: 2, name: 'FashionHub' },
          { id: 3, name: 'BookWorld' },
          { id: 4, name: 'HomeDecor' },
          { id: 5, name: 'SportsPro' }
        ]
      } catch (error) {
        console.error('Failed to load filter data:', error)
      }
    }

    // Lifecycle
    onMounted(() => {
      loadFilterData()
    })

    return {
      localReportType,
      localDateRange,
      localAdditionalFilters,
      activePreset,
      categories,
      sellers,
      datePresets,
      canApplyFilters,
      activeFiltersCount,
      handleReportTypeChange,
      handleDateChange,
      handleFiltersChange,
      applyDatePreset,
      resetFilters,
      applyFilters
    }
  }
}
</script>

<style scoped>
.report-filters {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.filters-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.reset-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background: #4b5563;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.filter-select,
.filter-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-input {
  flex: 1;
}

.date-separator {
  color: #6b7280;
  font-weight: 500;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preset-btn {
  background: white;
  border: 1px solid #d1d5db;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.preset-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.preset-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.amount-range,
.rating-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.amount-input {
  flex: 1;
}

.amount-separator {
  color: #6b7280;
  font-weight: 500;
}

.filters-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.apply-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.apply-btn:hover:not(:disabled) {
  background: #5a67d8;
}

.apply-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.filters-summary {
  color: #6b7280;
  font-size: 0.875rem;
}
</style>

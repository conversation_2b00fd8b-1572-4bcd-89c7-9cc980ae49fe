﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Category;

public class DeleteCategoryCommandHandler : IRequestHandler<DeleteCategoryCommand, bool>
{
    private readonly ICategoryRepository _repository;

    public DeleteCategoryCommandHandler(ICategoryRepository repository)
    {
        _repository = repository;
    }

    public async Task<bool> Handle(DeleteCategoryCommand request, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (item == null)
            return false;

        // Перевіряємо чи є підкатегорії
        var hasSubcategories = await _repository.HasSubcategoriesAsync(request.Id, cancellationToken);
        if (hasSubcategories)
        {
            throw new InvalidOperationException("Неможливо видалити категорію, оскільки вона містить підкатегорії. Спочатку видаліть або перемістіть всі підкатегорії.");
        }

        // Перевіряємо чи є продукти
        var hasProducts = await _repository.HasProductsAsync(request.Id, cancellationToken);
        if (hasProducts)
        {
            throw new InvalidOperationException("Неможливо видалити категорію, оскільки вона містить продукти. Спочатку перемістіть або видаліть всі продукти з цієї категорії.");
        }

        await _repository.DeleteAsync(item.Id, cancellationToken);
        return true;
    }
}



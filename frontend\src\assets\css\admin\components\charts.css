/* ===== ADMIN CHARTS SYSTEM ===== */

/* ===== CHART CONTAINERS ===== */
.admin-chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  min-height: 300px;
}

.admin-chart-container canvas {
  width: 100% !important;
  height: 100% !important;
  max-height: 300px;
}

/* ===== SALES CHART ===== */
.admin-sales-chart .admin-chart-container {
  height: 350px;
  min-height: 350px;
}

.admin-sales-chart .admin-chart-tabs {
  display: flex;
  align-items: center;
}

.admin-tab-group {
  display: flex;
  background: var(--admin-gray-100);
  border-radius: var(--admin-radius-md);
  padding: 2px;
}

.admin-tab {
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: none;
  background: transparent;
  color: var(--admin-gray-600);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  border-radius: var(--admin-radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-tab:hover {
  color: var(--admin-gray-800);
  background: var(--admin-gray-200);
}

.admin-tab.active {
  background: var(--admin-primary);
  color: white;
}

/* ===== ORDERS BY STATUS CHART ===== */
.admin-orders-chart .admin-chart-wrapper {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xl);
}

.admin-orders-chart .admin-chart-container {
  flex: 0 0 200px;
  height: 200px;
  min-height: 200px;
}

.admin-status-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-sm);
}

.admin-legend-item {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  background: var(--admin-gray-50);
}

.admin-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--admin-radius-full);
  flex-shrink: 0;
}

.admin-legend-label {
  flex: 1;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-700);
  text-transform: capitalize;
}

.admin-legend-value {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
}

/* ===== SITE PROFIT CHART ===== */
.admin-profit-chart .admin-chart-container {
  height: 300px;
  min-height: 300px;
}

/* ===== LOADING STATES ===== */
.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--admin-gray-500);
}

.admin-spinner {
  font-size: 2rem;
  margin-bottom: var(--admin-space-md);
  color: var(--admin-primary);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

/* ===== EMPTY STATES ===== */
.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--admin-gray-400);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-500);
  text-align: center;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .admin-orders-chart .admin-chart-wrapper {
    flex-direction: column;
    gap: var(--admin-space-lg);
  }
  
  .admin-orders-chart .admin-chart-container {
    flex: none;
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
  }
  
  .admin-status-legend {
    width: 100%;
  }
  
  .admin-tab-group {
    width: 100%;
    justify-content: center;
  }
  
  .admin-tab {
    flex: 1;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .admin-chart-container {
    height: 250px;
    min-height: 250px;
  }
  
  .admin-sales-chart .admin-chart-container {
    height: 280px;
    min-height: 280px;
  }
  
  .admin-orders-chart .admin-chart-container {
    height: 180px;
    min-height: 180px;
  }
}

/* ===== CHART ANIMATIONS ===== */
.admin-chart-container {
  animation: chartFadeIn 0.5s ease-out;
}

@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== CHART TOOLTIPS ===== */
.chartjs-tooltip {
  background: var(--admin-gray-900) !important;
  border: 1px solid var(--admin-primary) !important;
  border-radius: var(--admin-radius-md) !important;
  color: white !important;
  font-family: var(--admin-font-family) !important;
  font-size: var(--admin-text-sm) !important;
  padding: var(--admin-space-md) !important;
}

.chartjs-tooltip-title {
  font-weight: var(--admin-font-semibold) !important;
  margin-bottom: var(--admin-space-xs) !important;
}

.chartjs-tooltip-body {
  font-weight: var(--admin-font-medium) !important;
}

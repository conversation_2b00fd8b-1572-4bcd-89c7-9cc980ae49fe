﻿﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Category;

public class BulkDeleteCategoryCommandHandler : IRequestHandler<BulkDeleteCategoryCommand, int>
{
    private readonly ICategoryRepository _repository;

    public BulkDeleteCategoryCommandHandler(ICategoryRepository repository)
    {
        _repository = repository;
    }

    public async Task<int> Handle(BulkDeleteCategoryCommand request, CancellationToken cancellationToken)
    {
        int deletedCount = 0;
        var errors = new List<string>();

        foreach (var id in request.Ids)
        {
            var item = await _repository.GetByIdAsync(id, cancellationToken);
            if (item != null)
            {
                try
                {
                    // Перевіряємо чи є підкатегорії
                    var hasSubcategories = await _repository.HasSubcategoriesAsync(id, cancellationToken);
                    if (hasSubcategories)
                    {
                        errors.Add($"Категорія '{item.Name}' містить підкатегорії");
                        continue;
                    }

                    // Перевіряємо чи є продукти
                    var hasProducts = await _repository.HasProductsAsync(id, cancellationToken);
                    if (hasProducts)
                    {
                        errors.Add($"Категорія '{item.Name}' містить продукти");
                        continue;
                    }

                    await _repository.DeleteAsync(item.Id, cancellationToken);
                    deletedCount++;
                }
                catch (Exception ex)
                {
                    errors.Add($"Помилка при видаленні категорії '{item.Name}': {ex.Message}");
                }
            }
        }

        // Якщо є помилки, кидаємо виключення з детальною інформацією
        if (errors.Any())
        {
            throw new InvalidOperationException($"Не вдалося видалити деякі категорії:\n{string.Join("\n", errors)}");
        }

        return deletedCount;
    }
}

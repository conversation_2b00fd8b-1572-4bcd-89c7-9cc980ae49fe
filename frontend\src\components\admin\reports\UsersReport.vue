<template>
  <div class="users-report">
    <!-- User Metrics Grid -->
    <div class="metrics-section">
      <h3 class="section-title">
        <i class="fas fa-users"></i>
        User Analytics
      </h3>
      
      <div class="metrics-grid">
        <div 
          v-for="metric in userMetrics" 
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-change" v-if="metric.changePercentage">
              <i :class="getChangeIcon(metric.changePercentage)"></i>
              <span>{{ Math.abs(metric.changePercentage).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-comparison" v-if="metric.previousValue">
              vs {{ formatMetricValue(metric.previousValue, metric.type) }} last period
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Growth Charts -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- User Registration Trend -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">User Registration Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="registrationChartCanvas" id="registration-chart"></canvas>
          </div>
        </div>

        <!-- User Activity -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">User Activity</h3>
          </div>
          
          <div class="chart-content">
            <canvas ref="activityChartCanvas" id="activity-chart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- User Demographics -->
    <div class="demographics-section">
      <h3 class="section-title">
        <i class="fas fa-chart-pie"></i>
        User Demographics
      </h3>
      
      <div class="demographics-grid">
        <!-- Age Distribution -->
        <div class="demographic-card">
          <div class="demographic-header">
            <h4>Age Distribution</h4>
            <i class="fas fa-birthday-cake"></i>
          </div>
          <div class="age-groups">
            <div 
              v-for="group in ageGroups" 
              :key="group.range"
              class="age-group"
            >
              <div class="group-label">{{ group.range }}</div>
              <div class="group-bar">
                <div 
                  class="group-fill" 
                  :style="{ width: group.percentage + '%' }"
                ></div>
              </div>
              <div class="group-stats">
                <span class="group-count">{{ formatNumber(group.count) }}</span>
                <span class="group-percentage">{{ group.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Location Distribution -->
        <div class="demographic-card">
          <div class="demographic-header">
            <h4>Top Locations</h4>
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <div class="locations-list">
            <div 
              v-for="location in topLocations" 
              :key="location.city"
              class="location-item"
            >
              <div class="location-info">
                <div class="location-name">{{ location.city }}</div>
                <div class="location-region">{{ location.region }}</div>
              </div>
              <div class="location-stats">
                <div class="location-count">{{ formatNumber(location.users) }}</div>
                <div class="location-percentage">{{ location.percentage.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- User Types -->
        <div class="demographic-card">
          <div class="demographic-header">
            <h4>User Types</h4>
            <i class="fas fa-user-tag"></i>
          </div>
          <div class="user-types">
            <div 
              v-for="type in userTypes" 
              :key="type.type"
              class="user-type"
              :class="type.type.toLowerCase()"
            >
              <div class="type-icon">
                <i :class="type.icon"></i>
              </div>
              <div class="type-info">
                <div class="type-label">{{ type.type }}</div>
                <div class="type-count">{{ formatNumber(type.count) }} users</div>
                <div class="type-percentage">{{ type.percentage.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Engagement -->
    <div class="engagement-section">
      <h3 class="section-title">
        <i class="fas fa-chart-line"></i>
        User Engagement
      </h3>
      
      <div class="engagement-grid">
        <!-- Session Duration -->
        <div class="engagement-card">
          <div class="engagement-header">
            <h4>Average Session Duration</h4>
            <i class="fas fa-clock"></i>
          </div>
          <div class="session-stats">
            <div class="session-duration">
              <span class="duration-value">{{ averageSessionDuration }}</span>
              <span class="duration-label">minutes</span>
            </div>
            <div class="session-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label">Desktop:</span>
                <span class="breakdown-value">{{ desktopSessionDuration }} min</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label">Mobile:</span>
                <span class="breakdown-value">{{ mobileSessionDuration }} min</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Page Views -->
        <div class="engagement-card">
          <div class="engagement-header">
            <h4>Pages per Session</h4>
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="pageview-stats">
            <div class="pageview-average">
              <span class="pageview-value">{{ averagePageViews }}</span>
              <span class="pageview-label">pages</span>
            </div>
            <div class="pageview-distribution">
              <div 
                v-for="range in pageViewRanges" 
                :key="range.range"
                class="pageview-range"
              >
                <span class="range-label">{{ range.range }}</span>
                <div class="range-bar">
                  <div 
                    class="range-fill" 
                    :style="{ width: range.percentage + '%' }"
                  ></div>
                </div>
                <span class="range-percentage">{{ range.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Return Rate -->
        <div class="engagement-card">
          <div class="engagement-header">
            <h4>Return Rate</h4>
            <i class="fas fa-redo"></i>
          </div>
          <div class="return-stats">
            <div class="return-rate">
              <span class="rate-value">{{ returnRate }}%</span>
              <span class="rate-label">return within 30 days</span>
            </div>
            <div class="return-breakdown">
              <div class="return-item">
                <span class="return-period">7 days:</span>
                <span class="return-percentage">{{ sevenDayReturn }}%</span>
              </div>
              <div class="return-item">
                <span class="return-period">30 days:</span>
                <span class="return-percentage">{{ thirtyDayReturn }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Insights -->
    <div class="insights-section">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        User Insights
      </h3>
      
      <div class="insights-grid">
        <div 
          v-for="insight in userInsights" 
          :key="insight.id"
          class="insight-card"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
            <div class="insight-action" v-if="insight.action">
              <button class="action-btn" @click="handleInsightAction(insight)">
                {{ insight.action }}
              </button>
            </div>
          </div>
          <div class="insight-priority">
            <div class="priority-indicator" :class="`priority-${insight.priority}`">
              {{ insight.priority }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Users Table -->
    <div class="users-table-section">
      <h3 class="section-title">
        <i class="fas fa-table"></i>
        User Details
      </h3>
      
      <div class="table-controls">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="userSearch" 
            type="text" 
            placeholder="Search users..."
            class="search-input"
          />
        </div>
        
        <div class="filter-controls">
          <select v-model="roleFilter" class="filter-select">
            <option value="">All Roles</option>
            <option value="buyer">Buyers</option>
            <option value="seller">Sellers</option>
            <option value="admin">Admins</option>
          </select>
          
          <select v-model="statusFilter" class="filter-select">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>
      
      <div class="table-container">
        <table class="users-table">
          <thead>
            <tr>
              <th @click="sortBy('name')" class="sortable">
                User
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('email')" class="sortable">
                Email
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('role')" class="sortable">
                Role
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('registrationDate')" class="sortable">
                Registered
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('lastActivity')" class="sortable">
                Last Activity
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('orders')" class="sortable">
                Orders
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('totalSpent')" class="sortable">
                Total Spent
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in paginatedUsers" :key="user.id" class="user-row">
              <td>
                <div class="user-cell">
                  <img :src="user.avatar || '/placeholder-avatar.jpg'" :alt="user.name" class="user-avatar" />
                  <div class="user-details">
                    <div class="user-name">{{ user.name }}</div>
                    <div class="user-id">ID: {{ user.id }}</div>
                  </div>
                </div>
              </td>
              <td>
                <span class="user-email">{{ user.email }}</span>
              </td>
              <td>
                <span class="role-badge" :class="user.role">{{ user.role }}</span>
              </td>
              <td>
                <span class="date-text">{{ formatDate(user.registrationDate) }}</span>
              </td>
              <td>
                <span class="date-text">{{ formatDate(user.lastActivity) }}</span>
              </td>
              <td>
                <span class="orders-count">{{ formatNumber(user.orders) }}</span>
              </td>
              <td>
                <span class="spent-amount">{{ formatCurrency(user.totalSpent) }}</span>
              </td>
              <td>
                <span class="status-indicator" :class="user.status">
                  {{ user.status }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button @click="viewUser(user)" class="action-btn view-btn">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button @click="editUser(user)" class="action-btn edit-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage = 1" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button 
          @click="currentPage--" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-left"></i>
        </button>
        
        <span class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        
        <button 
          @click="currentPage++" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button 
          @click="currentPage = totalPages" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'UsersReport',
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // Refs
    const registrationChartCanvas = ref(null)
    const activityChartCanvas = ref(null)
    const chartPeriod = ref('daily')
    const userSearch = ref('')
    const roleFilter = ref('')
    const statusFilter = ref('')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const sortField = ref('registrationDate')
    const sortDirection = ref('desc')

    // Chart instances
    let registrationChart = null
    let activityChart = null

    // Computed properties
    const userMetrics = computed(() => {
      return props.data?.metrics?.items || []
    })

    const userInsights = computed(() => {
      return props.data?.insights || []
    })

    const usersData = computed(() => {
      return props.data?.table?.data || []
    })

    const ageGroups = computed(() => {
      const groups = [
        { range: '18-24', min: 18, max: 24 },
        { range: '25-34', min: 25, max: 34 },
        { range: '35-44', min: 35, max: 44 },
        { range: '45-54', min: 45, max: 54 },
        { range: '55+', min: 55, max: 100 }
      ]

      const total = usersData.value.length

      return groups.map(group => {
        const count = usersData.value.filter(user => {
          const age = user.age || 30 // Default age if not provided
          return age >= group.min && age <= group.max
        }).length

        return {
          ...group,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const topLocations = computed(() => {
      const locationCounts = {}

      usersData.value.forEach(user => {
        const location = user.location || 'Unknown'
        locationCounts[location] = (locationCounts[location] || 0) + 1
      })

      const total = usersData.value.length

      return Object.entries(locationCounts)
        .map(([location, count]) => {
          const [city, region] = location.split(', ')
          return {
            city: city || location,
            region: region || '',
            users: count,
            percentage: total > 0 ? (count / total) * 100 : 0
          }
        })
        .sort((a, b) => b.users - a.users)
        .slice(0, 5)
    })

    const userTypes = computed(() => {
      const types = [
        { type: 'Buyer', icon: 'fas fa-shopping-cart', filter: (user) => user.role === 'buyer' },
        { type: 'Seller', icon: 'fas fa-store', filter: (user) => user.role === 'seller' },
        { type: 'Admin', icon: 'fas fa-user-shield', filter: (user) => user.role === 'admin' }
      ]

      const total = usersData.value.length

      return types.map(type => {
        const count = usersData.value.filter(type.filter).length

        return {
          ...type,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const averageSessionDuration = computed(() => {
      const sessions = usersData.value.map(user => user.sessionDuration || 15)
      const total = sessions.reduce((sum, duration) => sum + duration, 0)
      return sessions.length > 0 ? (total / sessions.length).toFixed(1) : '0'
    })

    const desktopSessionDuration = computed(() => {
      return (parseFloat(averageSessionDuration.value) * 1.2).toFixed(1)
    })

    const mobileSessionDuration = computed(() => {
      return (parseFloat(averageSessionDuration.value) * 0.8).toFixed(1)
    })

    const averagePageViews = computed(() => {
      const pageViews = usersData.value.map(user => user.pageViews || 5)
      const total = pageViews.reduce((sum, views) => sum + views, 0)
      return pageViews.length > 0 ? (total / pageViews.length).toFixed(1) : '0'
    })

    const pageViewRanges = computed(() => {
      const ranges = [
        { range: '1-3 pages', min: 1, max: 3 },
        { range: '4-7 pages', min: 4, max: 7 },
        { range: '8+ pages', min: 8, max: 100 }
      ]

      const total = usersData.value.length

      return ranges.map(range => {
        const count = usersData.value.filter(user => {
          const views = user.pageViews || 5
          return views >= range.min && views <= range.max
        }).length

        return {
          ...range,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const returnRate = computed(() => {
      const returningUsers = usersData.value.filter(user => user.isReturning).length
      const total = usersData.value.length
      return total > 0 ? ((returningUsers / total) * 100).toFixed(1) : '0'
    })

    const sevenDayReturn = computed(() => {
      return (parseFloat(returnRate.value) * 0.6).toFixed(1)
    })

    const thirtyDayReturn = computed(() => {
      return returnRate.value
    })

    const filteredUsers = computed(() => {
      let filtered = usersData.value

      // Apply search filter
      if (userSearch.value) {
        const query = userSearch.value.toLowerCase()
        filtered = filtered.filter(user =>
          user.name?.toLowerCase().includes(query) ||
          user.email?.toLowerCase().includes(query) ||
          user.id?.toString().includes(query)
        )
      }

      // Apply role filter
      if (roleFilter.value) {
        filtered = filtered.filter(user => user.role === roleFilter.value)
      }

      // Apply status filter
      if (statusFilter.value) {
        filtered = filtered.filter(user => user.status === statusFilter.value)
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aVal = a[sortField.value]
        const bVal = b[sortField.value]

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
        }

        if (sortField.value.includes('Date') || sortField.value.includes('Activity')) {
          const aDate = new Date(aVal)
          const bDate = new Date(bVal)
          return sortDirection.value === 'asc' ? aDate - bDate : bDate - aDate
        }

        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortDirection.value === 'asc') {
          return aStr.localeCompare(bStr)
        } else {
          return bStr.localeCompare(aStr)
        }
      })

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredUsers.value.length / itemsPerPage.value)
    })

    const paginatedUsers = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredUsers.value.slice(start, end)
    })

    // Methods
    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatNumber = (value) => {
      return reportsService.formatNumber(value)
    }

    const formatDate = (value) => {
      return new Date(value).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const getChangeIcon = (changePercentage) => {
      if (changePercentage > 0) return 'fas fa-arrow-up'
      if (changePercentage < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const sortBy = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const updateChart = () => {
      createRegistrationChart()
    }

    const viewUser = (user) => {
      // TODO: Navigate to user details
      console.log('View user:', user)
    }

    const editUser = (user) => {
      // TODO: Navigate to user edit
      console.log('Edit user:', user)
    }

    const handleInsightAction = (insight) => {
      // TODO: Handle insight actions
      console.log('Handle insight action:', insight)
    }

    const createRegistrationChart = async () => {
      await nextTick()

      if (registrationChart) {
        registrationChart.destroy()
      }

      if (!registrationChartCanvas.value || !props.data?.charts?.primary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = registrationChartCanvas.value.getContext('2d')

        registrationChart = new Chart(ctx, {
          type: 'line',
          data: props.data.charts.primary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatNumber(context.parsed.y) + ' users'
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Users'
                },
                ticks: {
                  callback: function(value) {
                    return formatNumber(value)
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              axis: 'x',
              intersect: false
            }
          }
        })
      } catch (error) {
        console.error('Error creating registration chart:', error)
      }
    }

    const createActivityChart = async () => {
      await nextTick()

      if (activityChart) {
        activityChart.destroy()
      }

      if (!activityChartCanvas.value || !props.data?.charts?.secondary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = activityChartCanvas.value.getContext('2d')

        activityChart = new Chart(ctx, {
          type: 'bar',
          data: props.data.charts.secondary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatNumber(context.parsed.y) + ' users'
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Activity Level'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Users'
                },
                ticks: {
                  callback: function(value) {
                    return formatNumber(value)
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('Error creating activity chart:', error)
      }
    }

    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createRegistrationChart()
        createActivityChart()
      })
    }, { deep: true })

    watch([userSearch, roleFilter, statusFilter], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createRegistrationChart()
        createActivityChart()
      })
    })

    onUnmounted(() => {
      if (registrationChart) {
        registrationChart.destroy()
      }
      if (activityChart) {
        activityChart.destroy()
      }
    })

    return {
      registrationChartCanvas,
      activityChartCanvas,
      chartPeriod,
      userSearch,
      roleFilter,
      statusFilter,
      currentPage,
      itemsPerPage,
      userMetrics,
      userInsights,
      ageGroups,
      topLocations,
      userTypes,
      averageSessionDuration,
      desktopSessionDuration,
      mobileSessionDuration,
      averagePageViews,
      pageViewRanges,
      returnRate,
      sevenDayReturn,
      thirtyDayReturn,
      filteredUsers,
      totalPages,
      paginatedUsers,
      formatMetricValue,
      formatCurrency,
      formatNumber,
      formatDate,
      getChangeIcon,
      sortBy,
      updateChart,
      viewUser,
      editUser,
      handleInsightAction
    }
  }
}
</script>

<style scoped>
@import './UsersReportStyles.css';
.users-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Metrics Section */
.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #f8fafc 100%);
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #f8fafc 100%);
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.positive .metric-change {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.negative .metric-change {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.neutral .metric-change {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-comparison {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Charts Section */
.charts-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.chart-container {
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.chart-content {
  height: 300px;
  position: relative;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .chart-content {
    height: 250px;
  }

  .metric-value {
    font-size: 1.5rem;
  }
}
</style>

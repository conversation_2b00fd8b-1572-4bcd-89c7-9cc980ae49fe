<template>
  <div class="admin-page-container">
    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading product details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-error-state">
      <AdminCard variant="danger" title="Error Loading Product">
        <div class="admin-error-content">
          <i class="fas fa-exclamation-triangle admin-error-icon"></i>
          <p class="admin-error-message">{{ error }}</p>
          <button class="admin-btn admin-btn-primary" @click="fetchProduct">
            <i class="fas fa-redo"></i>
            Try Again
          </button>
        </div>
      </AdminCard>
    </div>

    <!-- Product Details -->
    <div v-else-if="product" class="admin-page-content">
      <!-- Header -->
      <AdminProductHeader
        mode="view"
        :product="product"
        :title="product.name"
        :subtitle="`Product ID: ${product.id}`"
        @delete="handleDelete"
        @duplicate="handleDuplicate"
      />

      <!-- Main Content Grid -->
      <div class="admin-content-grid">
        <!-- Product Information Card -->
        <AdminProductInfoCard
          :product="enrichedProduct"
          :loading="loading"
          @edit="handleEdit"
        />

        <!-- Product Attributes Card -->
        <AdminProductAttributesTable
          :attributes="product.attributes"
          :loading="loading"
        />

        <!-- Product Images Card -->
        <AdminProductImagesViewer
          :product="product"
          :loading="loading"
        />

        <!-- Product Metadata Card -->
        <AdminProductMetadataCard
          :product="product"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Not Found State -->
    <div v-else class="admin-not-found-state">
      <AdminCard variant="warning" title="Product Not Found">
        <div class="admin-not-found-content">
          <i class="fas fa-search admin-not-found-icon"></i>
          <p class="admin-not-found-message">
            The requested product could not be found or may have been deleted.
          </p>
          <router-link to="/admin/products" class="admin-btn admin-btn-primary">
            <i class="fas fa-arrow-left"></i>
            Back to Products
          </router-link>
        </div>
      </AdminCard>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AdminCard from '../common/AdminCard.vue';
import AdminProductHeader from './AdminProductHeader.vue';
import AdminProductInfoCard from './AdminProductInfoCard.vue';
import AdminProductAttributesTable from './AdminProductAttributesTable.vue';
import AdminProductImagesViewer from './AdminProductImagesViewer.vue';
import AdminProductMetadataCard from './AdminProductMetadataCard.vue';
import { productsService } from '../../services/products.js';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const product = ref(null);
const loading = ref(false);
const error = ref(null);

// Computed
const enrichedProduct = computed(() => {
  if (!product.value) return null;

  return {
    ...product.value,
    // Add any additional computed properties here
  };
});

// Methods
const fetchProduct = async () => {
  try {
    loading.value = true;
    error.value = null;

    const productId = route.params.id;
    const response = await productsService.getProductById(productId);

    // Handle different response structures
    product.value = response.data || response;
  } catch (err) {
    console.error('Error fetching product:', err);
    error.value = err.message || 'Failed to load product details';
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  router.push(`/admin/products/${product.value.id}/edit`);
};

const handleDelete = async () => {
  if (!confirm('Are you sure you want to delete this product?')) return;

  try {
    await productsService.deleteProduct(product.value.id);
    router.push('/admin/products');
  } catch (err) {
    console.error('Error deleting product:', err);
    error.value = 'Failed to delete product';
  }
};

const handleDuplicate = async () => {
  try {
    // Create a copy of the product without ID and timestamps
    const duplicateData = {
      ...product.value,
      name: `${product.value.name} (Copy)`,
      slug: `${product.value.slug}-copy`,
      id: undefined,
      createdAt: undefined,
      updatedAt: undefined
    };

    const newProduct = await productsService.createProduct(duplicateData);
    const productId = newProduct.data?.id || newProduct.id;
    router.push(`/admin/products/${productId}/edit`);
  } catch (err) {
    console.error('Error duplicating product:', err);
    error.value = 'Failed to duplicate product';
  }
};

// Lifecycle
onMounted(() => {
  fetchProduct();
});
</script>
<style scoped>
.admin-page-container {
  min-height: 100vh;
  background: var(--admin-bg-secondary);
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.admin-loading-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-lg);
  color: var(--admin-text-muted);
  margin: 0;
}

.admin-error-state,
.admin-not-found-state {
  max-width: 600px;
  margin: var(--admin-space-xl) auto;
}

.admin-error-content,
.admin-not-found-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--admin-space-md);
}

.admin-error-icon,
.admin-not-found-icon {
  font-size: 3rem;
  color: var(--admin-danger);
}

.admin-error-message,
.admin-not-found-message {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin: 0;
  line-height: 1.6;
}

.admin-content-grid {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--admin-space-lg);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-content-grid {
    padding: 0 var(--admin-space-md);
    gap: var(--admin-space-md);
  }
}
</style>



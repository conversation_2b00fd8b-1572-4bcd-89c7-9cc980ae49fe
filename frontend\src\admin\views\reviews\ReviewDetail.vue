<template>
  <div class="review-detail">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <nav class="breadcrumb">
            <ul>
              <li><router-link to="/admin/reviews">Reviews</router-link></li>
              <li class="is-active"><a>Review Details</a></li>
            </ul>
          </nav>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="fetchReview" :class="{ 'is-loading': loading }">
            <span class="icon"><i class="fas fa-sync-alt"></i></span>
            <span>Refresh</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="has-text-centered" v-if="loading && !review.id">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-3x"></i>
      </span>
      <p class="mt-3">Loading review details...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Review Details -->
    <div v-else-if="review.id">
      <div class="columns">
        <!-- Main Info -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Review Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Product</label>
                    <p class="info-value">
                      <router-link :to="{ name: 'AdminProductDetail', params: { id: review.productId } }">
                        {{ review.productName }}
                      </router-link>
                    </p>
                  </div>
                  <div class="field">
                    <label class="label">User</label>
                    <p class="info-value">
                      <router-link :to="{ name: 'AdminUserDetail', params: { id: review.userId } }">
                        {{ review.userName }}
                      </router-link>
                    </p>
                  </div>
                  <div class="field">
                    <label class="label">Rating</label>
                    <p class="info-value">
                      <div class="stars">
                        <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= review.rating }">
                          ★
                        </span>
                        <span class="ml-2">({{ review.rating }}/5)</span>
                      </div>
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Created At</label>
                    <p class="info-value">{{ formatDateTime(review.createdAt) }}</p>
                  </div>
                  <div class="field" v-if="review.updatedAt">
                    <label class="label">Updated At</label>
                    <p class="info-value">{{ formatDateTime(review.updatedAt) }}</p>
                  </div>
                </div>
              </div>
              
              <div class="field" v-if="review.comment">
                <label class="label">Comment</label>
                <div class="content">
                  <p>{{ review.comment }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Related Rating -->
          <div class="card mt-4" v-if="review.rating">
            <div class="card-header">
              <p class="card-header-title">Detailed Rating</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Service</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= review.serviceRating }">
                        ★
                      </span>
                      <span class="ml-2">({{ review.serviceRating || 'N/A' }})</span>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Delivery Time</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= review.deliveryTimeRating }">
                        ★
                      </span>
                      <span class="ml-2">({{ review.deliveryTimeRating || 'N/A' }})</span>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Accuracy</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= review.accuracyRating }">
                        ★
                      </span>
                      <span class="ml-2">({{ review.accuracyRating || 'N/A' }})</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Actions</p>
            </div>
            <div class="card-content">
              <div class="buttons is-fullwidth">
                <button 
                  class="button is-danger is-fullwidth"
                  @click="showDeleteModal = true">
                  <span class="icon"><i class="fas fa-trash"></i></span>
                  <span>Delete Review</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Review Stats -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Statistics</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Helpful Votes</label>
                <p class="info-value">{{ review.helpfulVotes || 0 }}</p>
              </div>
              <div class="field">
                <label class="label">Total Votes</label>
                <p class="info-value">{{ review.totalVotes || 0 }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Delete Review</p>
          <button class="delete" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete this review? This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger" 
            @click="deleteReview"
            :class="{ 'is-loading': actionLoading }">
            Delete Review
          </button>
          <button class="button" @click="showDeleteModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { reviewsService } from '@/admin/services/reviews';

const route = useRoute();
const router = useRouter();

// Reactive data
const review = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showDeleteModal = ref(false);

// Methods
const fetchReview = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const response = await reviewsService.getReviewById(route.params.id);
    review.value = response;
  } catch (err) {
    error.value = err.message || 'Failed to load review details';
  } finally {
    loading.value = false;
  }
};

const deleteReview = async () => {
  actionLoading.value = true;
  try {
    await reviewsService.deleteReview(review.value.id);
    router.push({ name: 'AdminReviews' });
  } catch (err) {
    error.value = err.message || 'Failed to delete review';
  } finally {
    actionLoading.value = false;
    showDeleteModal.value = false;
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
  fetchReview();
});
</script>

<style scoped>
.review-detail {
  padding: 1rem;
}

.info-value {
  font-weight: 500;
  color: #363636;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1.2em;
}

.star.is-filled {
  color: #ffd700;
}

.content p {
  white-space: pre-wrap;
}
</style>
